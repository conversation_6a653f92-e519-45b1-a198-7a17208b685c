"""
API中间件模块

提供认证、速率限制、安全验证等中间件功能
"""

import time
import asyncio
from typing import Dict, List, Optional, Any, Callable
from fastapi import Request, Response, HTTPException, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse
import ipaddress

from src.common.logger import get_logger
from src.common.config import Settings

logger = get_logger(__name__)


class RateLimiter:
    """速率限制器"""
    
    def __init__(self, max_requests: int = 100, window_seconds: int = 60):
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self._requests: Dict[str, List[float]] = {}
        self._lock = asyncio.Lock()
    
    async def is_allowed(self, client_id: str) -> bool:
        """检查是否允许请求"""
        async with self._lock:
            current_time = time.time()
            
            # 获取客户端请求历史
            if client_id not in self._requests:
                self._requests[client_id] = []
            
            requests = self._requests[client_id]
            
            # 清理过期请求
            cutoff_time = current_time - self.window_seconds
            requests[:] = [req_time for req_time in requests if req_time > cutoff_time]
            
            # 检查是否超过限制
            if len(requests) >= self.max_requests:
                return False
            
            # 记录当前请求
            requests.append(current_time)
            return True
    
    def get_remaining_requests(self, client_id: str) -> int:
        """获取剩余请求数"""
        if client_id not in self._requests:
            return self.max_requests
        
        current_count = len(self._requests[client_id])
        return max(0, self.max_requests - current_count)


class SecurityMiddleware(BaseHTTPMiddleware):
    """安全中间件"""
    
    def __init__(
        self,
        app,
        settings: Optional[Settings] = None,
        enable_rate_limiting: bool = True,
        enable_cors: bool = True,
        enable_security_headers: bool = True
    ):
        super().__init__(app)
        self.settings = settings or Settings()
        self.enable_rate_limiting = enable_rate_limiting
        self.enable_cors = enable_cors
        self.enable_security_headers = enable_security_headers
        
        # 初始化速率限制器
        if self.enable_rate_limiting:
            self.rate_limiter = RateLimiter(
                max_requests=self.settings.security.rate_limit_per_minute,
                window_seconds=60
            )
        
        # 可信IP列表
        self.trusted_ips = [
            "127.0.0.1",
            "::1",
            "localhost"
        ]
        
        # 排除路径（不进行速率限制的路径）
        self.excluded_paths = [
            "/health",
            "/metrics",
            "/docs",
            "/openapi.json"
        ]
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """中间件处理逻辑"""
        try:
            # 获取客户端IP
            client_ip = self._get_client_ip(request)
            
            # 安全检查
            if not await self._security_check(request, client_ip):
                return JSONResponse(
                    status_code=403,
                    content={"error": "Access denied"}
                )
            
            # 速率限制检查
            if self.enable_rate_limiting and not self._is_excluded_path(request.url.path):
                if not await self._rate_limit_check(client_ip):
                    return JSONResponse(
                        status_code=429,
                        content={
                            "error": "Rate limit exceeded",
                            "retry_after": 60
                        },
                        headers={"Retry-After": "60"}
                    )
            
            # 处理请求
            start_time = time.time()
            response = await call_next(request)
            process_time = time.time() - start_time
            
            # 添加安全头
            if self.enable_security_headers:
                self._add_security_headers(response)
            
            # 添加处理时间头
            response.headers["X-Process-Time"] = str(process_time)
            
            # 记录请求日志
            logger.info(
                f"Request processed: {request.method} {request.url.path}",
                extra={
                    "client_ip": client_ip,
                    "status_code": response.status_code,
                    "process_time": process_time
                }
            )
            
            return response
            
        except Exception as e:
            logger.error(f"Middleware error: {e}")
            return JSONResponse(
                status_code=500,
                content={"error": "Internal server error"}
            )
    
    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP地址"""
        # 检查代理头
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # 使用客户端IP
        return request.client.host if request.client else "unknown"
    
    async def _security_check(self, request: Request, client_ip: str) -> bool:
        """安全检查"""
        try:
            # IP白名单检查（开发模式下跳过）
            if not self.settings.is_development:
                if not self._is_trusted_ip(client_ip):
                    # 检查IP是否在黑名单中（这里简化处理）
                    if self._is_blocked_ip(client_ip):
                        logger.warning(f"Blocked IP attempted access: {client_ip}")
                        return False
            
            # User-Agent检查
            user_agent = request.headers.get("User-Agent", "")
            if self._is_suspicious_user_agent(user_agent):
                logger.warning(f"Suspicious User-Agent: {user_agent} from {client_ip}")
                # 不直接拒绝，但记录警告
            
            # 请求大小检查
            content_length = request.headers.get("Content-Length")
            if content_length and int(content_length) > 10 * 1024 * 1024:  # 10MB
                logger.warning(f"Large request from {client_ip}: {content_length} bytes")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Security check error: {e}")
            return False
    
    def _is_trusted_ip(self, client_ip: str) -> bool:
        """检查是否为可信IP"""
        try:
            ip = ipaddress.ip_address(client_ip)
            
            # 检查私有网络
            if ip.is_private or ip.is_loopback:
                return True
            
            # 检查可信IP列表
            for trusted_ip in self.trusted_ips:
                if client_ip == trusted_ip:
                    return True
            
            return False
            
        except ValueError:
            return False
    
    def _is_blocked_ip(self, client_ip: str) -> bool:
        """检查是否为被阻止的IP"""
        # 这里可以集成黑名单数据库或外部服务
        blocked_ips = [
            # 示例黑名单IP
        ]
        return client_ip in blocked_ips
    
    def _is_suspicious_user_agent(self, user_agent: str) -> bool:
        """检查是否为可疑的User-Agent"""
        suspicious_patterns = [
            "bot",
            "crawler",
            "spider",
            "scanner",
            "curl",
            "wget"
        ]
        
        user_agent_lower = user_agent.lower()
        return any(pattern in user_agent_lower for pattern in suspicious_patterns)
    
    async def _rate_limit_check(self, client_ip: str) -> bool:
        """速率限制检查"""
        try:
            return await self.rate_limiter.is_allowed(client_ip)
        except Exception as e:
            logger.error(f"Rate limit check error: {e}")
            return True  # 出错时允许请求
    
    def _is_excluded_path(self, path: str) -> bool:
        """检查是否为排除路径"""
        return any(path.startswith(excluded) for excluded in self.excluded_paths)
    
    def _add_security_headers(self, response: Response) -> None:
        """添加安全头"""
        security_headers = {
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
            "Referrer-Policy": "strict-origin-when-cross-origin"
        }
        
        for header, value in security_headers.items():
            response.headers[header] = value


class AuthenticationMiddleware:
    """认证中间件"""
    
    def __init__(self, auth_manager=None):
        self.auth_manager = auth_manager
        self.security = HTTPBearer(auto_error=False)
    
    async def authenticate(
        self,
        credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False))
    ) -> Optional[Dict[str, Any]]:
        """认证用户"""
        if not credentials:
            return None
        
        if not self.auth_manager:
            logger.warning("Authentication manager not available")
            return None
        
        try:
            # 验证JWT令牌
            token_info = await self.auth_manager.verify_token(credentials.credentials)
            if token_info:
                return token_info
            
            raise HTTPException(
                status_code=401,
                detail="Invalid authentication credentials",
                headers={"WWW-Authenticate": "Bearer"}
            )
            
        except Exception as e:
            logger.error(f"Authentication error: {e}")
            raise HTTPException(
                status_code=401,
                detail="Authentication failed",
                headers={"WWW-Authenticate": "Bearer"}
            )
    
    def require_permission(self, permission: str):
        """需要特定权限的装饰器"""
        def decorator(func):
            async def wrapper(*args, **kwargs):
                # 获取当前用户信息
                token_info = kwargs.get('current_user')
                if not token_info:
                    raise HTTPException(status_code=401, detail="Authentication required")
                
                # 检查权限
                user_role = token_info.get('role')
                if not self.auth_manager.has_permission(user_role, permission):
                    raise HTTPException(status_code=403, detail="Insufficient permissions")
                
                return await func(*args, **kwargs)
            return wrapper
        return decorator


class CORSMiddleware(BaseHTTPMiddleware):
    """CORS中间件"""
    
    def __init__(
        self,
        app,
        allowed_origins: List[str] = None,
        allowed_methods: List[str] = None,
        allowed_headers: List[str] = None
    ):
        super().__init__(app)
        self.allowed_origins = allowed_origins or ["*"]
        self.allowed_methods = allowed_methods or ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
        self.allowed_headers = allowed_headers or ["*"]
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """CORS处理"""
        # 预检请求处理
        if request.method == "OPTIONS":
            response = Response()
            self._add_cors_headers(response, request)
            return response
        
        # 正常请求处理
        response = await call_next(request)
        self._add_cors_headers(response, request)
        return response
    
    def _add_cors_headers(self, response: Response, request: Request) -> None:
        """添加CORS头"""
        origin = request.headers.get("Origin")
        
        if origin and (self.allowed_origins == ["*"] or origin in self.allowed_origins):
            response.headers["Access-Control-Allow-Origin"] = origin
        
        response.headers["Access-Control-Allow-Methods"] = ", ".join(self.allowed_methods)
        response.headers["Access-Control-Allow-Headers"] = ", ".join(self.allowed_headers)
        response.headers["Access-Control-Allow-Credentials"] = "true"


class LoggingMiddleware(BaseHTTPMiddleware):
    """日志中间件"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """请求日志记录"""
        start_time = time.time()
        
        # 记录请求开始
        logger.info(
            f"Request started: {request.method} {request.url}",
            extra={
                "method": request.method,
                "url": str(request.url),
                "client": request.client.host if request.client else "unknown"
            }
        )
        
        try:
            response = await call_next(request)
            
            # 记录请求完成
            process_time = time.time() - start_time
            logger.info(
                f"Request completed: {request.method} {request.url} - {response.status_code}",
                extra={
                    "method": request.method,
                    "url": str(request.url),
                    "status_code": response.status_code,
                    "process_time": process_time
                }
            )
            
            return response
            
        except Exception as e:
            # 记录请求错误
            process_time = time.time() - start_time
            logger.error(
                f"Request failed: {request.method} {request.url} - {str(e)}",
                extra={
                    "method": request.method,
                    "url": str(request.url),
                    "error": str(e),
                    "process_time": process_time
                }
            )
            raise 