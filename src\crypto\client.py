"""
加密客户端

提供统一的加密/解密、签名/验签接口，支持多种算法和Vault集成
"""

import asyncio
import time
from typing import Dict, Optional, Any, Union
from uuid import UUID
from datetime import datetime, timedelta

from src.common.logger import get_logger
from .models import (
    CryptoRequest,
    CryptoResponse,
    CryptoOperation,
    EncryptionConfig,
    SignatureConfig,
    VaultConfig,
    CryptoStats,
    EncryptionAlgorithm,
    SignatureAlgorithm,
    HashAlgorithm,
)
import base64
import os
from cryptography.hazmat.primitives.ciphers.aead import AESGCM
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.exceptions import InvalidSignature
import hvac

logger = get_logger(__name__)


class CryptoClient:
    """
    加密客户端
    
    提供统一的加密/解密、签名/验签、哈希等功能接口
    支持Vault集成进行密钥管理
    """
    
    def __init__(self, vault_config: Optional[VaultConfig] = None, mode: str = "production"):
        self.logger = get_logger(__name__)
        self._vault_config = vault_config
        self._mode = mode  # 'production' or 'dev'
        self._initialized = False
        self._vault_client = None
        self._stats = CryptoStats()
        
        # 密钥缓存 - 带过期时间和大小限制
        self._key_cache: Dict[str, Dict[str, Any]] = {}
        self._cache_max_size = 1000  # 最大缓存条目数
        self._cache_ttl_minutes = 30  # 缓存过期时间（分钟）
        
    async def initialize(self) -> None:
        """初始化客户端"""
        try:
            self.logger.info("Initializing CryptoClient")
            
            # 初始化Vault连接
            if self._vault_config:
                await self._initialize_vault()
            
            # 验证算法支持
            await self._validate_algorithms()
            
            self._initialized = True
            self.logger.info("CryptoClient initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize CryptoClient: {e}")
            raise
    
    async def cleanup(self) -> None:
        """清理资源"""
        try:
            self.logger.info("Cleaning up CryptoClient")
            
            # 清理密钥缓存
            self._key_cache.clear()
            
            # 清理Vault连接
            if self._vault_client:
                # TODO: 关闭Vault连接
                pass
                
            self._initialized = False
            self.logger.info("CryptoClient cleaned up successfully")
            
        except Exception as e:
            self.logger.error(f"Error during CryptoClient cleanup: {e}")
    
    async def process_request(self, request: CryptoRequest) -> CryptoResponse:
        """
        处理加密请求
        
        Args:
            request: 加密请求
            
        Returns:
            CryptoResponse: 处理结果
        """
        start_time = time.time()
        
        try:
            self.logger.info(
                f"Processing crypto request",
                request_id=str(request.request_id),
                operation=request.operation
            )
            
            # 更新统计
            self._stats.total_operations += 1
            
            # 根据操作类型分发处理
            if request.operation == CryptoOperation.ENCRYPT:
                result = await self._encrypt(request)
            elif request.operation == CryptoOperation.DECRYPT:
                result = await self._decrypt(request)
            elif request.operation == CryptoOperation.SIGN:
                result = await self._sign(request)
            elif request.operation == CryptoOperation.VERIFY:
                result = await self._verify(request)
            elif request.operation == CryptoOperation.HASH:
                result = await self._hash(request)
            else:
                raise ValueError(f"Unsupported operation: {request.operation}")
            
            processing_time = (time.time() - start_time) * 1000  # 转换为毫秒
            
            response = CryptoResponse(
                request_id=request.request_id,
                success=True,
                operation=request.operation,
                result=result,
                processing_time=processing_time,
                metadata={"input_size": len(str(request.data))}
            )
            
            # 更新统计
            self._stats.successful_operations += 1
            self._stats.operations_by_type[request.operation] = (
                self._stats.operations_by_type.get(request.operation, 0) + 1
            )
            
            self.logger.info(
                f"Crypto request processed successfully",
                request_id=str(request.request_id),
                operation=request.operation,
                processing_time=processing_time
            )
            
            return response
            
        except Exception as e:
            processing_time = (time.time() - start_time) * 1000
            
            self.logger.error(
                f"Crypto request failed for request_id={request.request_id}, "
                f"operation={request.operation}, error: {e}"
            )
            
            # 更新统计
            self._stats.failed_operations += 1
            
            return CryptoResponse(
                request_id=request.request_id,
                success=False,
                operation=request.operation,
                error_message=str(e),
                error_code="CRYPTO_ERROR",
                processing_time=processing_time
            )
    
    async def get_stats(self) -> CryptoStats:
        """获取统计信息"""
        # 计算平均处理时间
        if self._stats.total_operations > 0:
            # 这里应该维护一个处理时间的历史记录来计算真实的平均值
            # 暂时返回估算值
            self._stats.average_processing_time = 50.0  # 假设50ms平均处理时间
            
        return self._stats
    
    async def _get_key(self, key_id: str) -> bytes:
        """
        临时密钥获取方法
        
        TODO: 替换为Vault集成
        """
        # 简单地基于key_id生成一个确定性的伪密钥用于测试
        from hashlib import sha256
        if "aes-256" in key_id.lower():
            return sha256(key_id.encode()).digest()
        elif "aes-128" in key_id.lower():
            return sha256(key_id.encode()).digest()[:16]
        else:
            # 默认返回32字节
            return sha256(b"default-secret-key-for-testing").digest()

    async def _encrypt(self, request: CryptoRequest) -> str:
        """加密数据"""
        if not request.encryption_config:
            raise ValueError("Encryption config is required for encrypt operation")
        
        config = request.encryption_config
        
        # 算法支持检查
        if config.algorithm not in [EncryptionAlgorithm.AES_GCM, EncryptionAlgorithm.SM4]:
            raise NotImplementedError(f"Algorithm {config.algorithm} not supported")

        # 安全日志：避免记录完整配置
        self.logger.info(
            f"Encrypting data",
            operation="encrypt",
            algorithm=config.algorithm.value,
            key_id=config.key_config.key_id
        )
        
        # 协议一致性验证 (设计文档5.2)
        self._validate_protocol(config)

        key = await self._get_key_from_vault(
            config.key_config.key_id, 
            config.key_config.vault_path
        )
        aesgcm = AESGCM(key)
        
        nonce = os.urandom(12)  # GCM推荐使用12字节的nonce
        
        data_to_encrypt = str(request.data).encode(request.encoding)
        
        ciphertext = aesgcm.encrypt(nonce, data_to_encrypt, None)
        
        # 将nonce和密文拼接在一起返回
        encrypted_data = nonce + ciphertext
        
        # 更新算法统计
        self._stats.operations_by_algorithm[config.algorithm] = (
            self._stats.operations_by_algorithm.get(config.algorithm, 0) + 1
        )
        
        return base64.b64encode(encrypted_data).decode()
    
    async def _decrypt(self, request: CryptoRequest) -> str:
        """解密数据"""
        if not request.encryption_config:
            raise ValueError("Encryption config is required for decrypt operation")
        
        config = request.encryption_config

        # 算法支持检查
        if config.algorithm not in [EncryptionAlgorithm.AES_GCM, EncryptionAlgorithm.SM4]:
            raise NotImplementedError(f"Algorithm {config.algorithm} not supported")
        
        # 安全日志：避免记录完整配置
        self.logger.info(
            f"Decrypting data",
            operation="decrypt",
            algorithm=config.algorithm.value,
            key_id=config.key_config.key_id
        )
        
        # 协议一致性验证 (设计文档5.2)
        self._validate_protocol(config)
        
        key = await self._get_key_from_vault(
            config.key_config.key_id,
            config.key_config.vault_path
        )
        aesgcm = AESGCM(key)
        
        try:
            encrypted_data = base64.b64decode(str(request.data))
            nonce = encrypted_data[:12]
            ciphertext = encrypted_data[12:]
            
            decrypted_data = aesgcm.decrypt(nonce, ciphertext, None)

            # 更新算法统计
            self._stats.operations_by_algorithm[config.algorithm] = (
                self._stats.operations_by_algorithm.get(config.algorithm, 0) + 1
            )
        
            return decrypted_data.decode(request.encoding)
        except Exception as e:
            self.logger.error(f"Decryption failed: {e}")
            raise ValueError("Decryption failed. The data may be corrupt or the key incorrect.")
    
    async def _sign(self, request: CryptoRequest) -> str:
        """签名数据"""
        if not request.signature_config:
            raise ValueError("Signature config is required for sign operation")
        
        config = request.signature_config
        # 支持算法检查
        if config.algorithm not in [SignatureAlgorithm.RSA_PSS, SignatureAlgorithm.SM2]:
            raise NotImplementedError(f"Algorithm {config.algorithm} not supported")
            
        # 安全日志：避免记录完整配置
        self.logger.info(
            f"Signing data",
            operation="sign",
            algorithm=config.algorithm.value,
            key_id=config.key_config.key_id
        )
        
        # 协议一致性验证 (设计文档5.2)
        self._validate_protocol(config)

        # 获取私钥
        private_key = await self._get_key_from_vault(
            config.key_config.key_id,
            config.key_config.vault_path
        )

        if not isinstance(private_key, rsa.RSAPrivateKey):
            raise TypeError("RSA private key is required for signing.")

        # 获取哈希算法对象
        if config.hash_algorithm == HashAlgorithm.SHA256:
            hash_algo = hashes.SHA256()
        elif config.hash_algorithm == HashAlgorithm.SHA1:
            hash_algo = hashes.SHA1()
        else:
            # 默认使用SHA256
            hash_algo = hashes.SHA256()

        # 使用私钥签名
        signature = private_key.sign(
            request.data.encode(),
            padding.PSS(
                mgf=padding.MGF1(hash_algo),
                salt_length=padding.PSS.MAX_LENGTH
            ),
            hash_algo
        )
        
        return base64.b64encode(signature).decode()

    async def _verify(self, request: CryptoRequest) -> bool:
        """验证签名"""
        if not request.signature_config:
            raise ValueError("Signature config is required for verify operation")
        
        config = request.signature_config
        if not request.metadata or 'signature' not in request.metadata:
            raise ValueError("Signature is required in metadata for verify operation")
        
        # 支持算法检查
        if config.algorithm not in [SignatureAlgorithm.RSA_PSS, SignatureAlgorithm.SM2]:
            raise NotImplementedError(f"Algorithm {config.algorithm} not supported")

        # 安全日志：避免记录完整配置
        self.logger.info(
            f"Verifying signature",
            operation="verify",
            algorithm=config.algorithm.value,
            key_id=config.key_config.key_id
        )
        
        # 协议一致性验证 (设计文档5.2)
        self._validate_protocol(config)

        # 安全地获取公钥
        public_key = await self._get_key_from_vault(
            config.key_config.key_id,
            config.key_config.vault_path
        )

        if not isinstance(public_key, rsa.RSAPublicKey):
            # 尝试从私钥获取公钥
            if isinstance(public_key, rsa.RSAPrivateKey):
                public_key = public_key.public_key()
            else:
                raise TypeError("RSA public key is required for verification.")
        
        # 获取哈希算法对象
        if config.hash_algorithm == HashAlgorithm.SHA256:
            hash_algo = hashes.SHA256()
        elif config.hash_algorithm == HashAlgorithm.SHA1:
            hash_algo = hashes.SHA1()
        else:
            # 默认使用SHA256
            hash_algo = hashes.SHA256()

        signature = base64.b64decode(request.metadata['signature'])

        try:
            public_key.verify(
                signature,
                request.data.encode(),
                padding.PSS(
                    mgf=padding.MGF1(hash_algo),
                    salt_length=padding.PSS.MAX_LENGTH
                ),
                hash_algo
            )
            return True
        except InvalidSignature:
            self.logger.warning("Signature verification failed: InvalidSignature")
            return False
        except Exception as e:
            self.logger.error(f"An unexpected error occurred during signature verification: {e}")
            return False
    
    async def _hash(self, request: CryptoRequest) -> str:
        """计算哈希"""
        if not request.hash_algorithm:
            raise ValueError("Hash algorithm is required for hash operation")
        
        algorithm = request.hash_algorithm
        
        # 安全日志：避免记录完整配置
        self.logger.info(
            f"Hashing data",
            operation="hash",
            algorithm=algorithm.value
        )
        
        # 更新算法统计
        self._stats.operations_by_algorithm[algorithm] = (
            self._stats.operations_by_algorithm.get(algorithm, 0) + 1
        )
        
        # 实现基本的哈希计算
        import hashlib
        
        data_str = str(request.data)
        
        if algorithm == HashAlgorithm.SHA256:
            hash_obj = hashlib.sha256(data_str.encode())
        elif algorithm == HashAlgorithm.SHA1:
            hash_obj = hashlib.sha1(data_str.encode())
        elif algorithm == HashAlgorithm.MD5:
            hash_obj = hashlib.md5(data_str.encode())
        else:
            # 对于SM3等暂不支持的算法，返回SHA256结果
            hash_obj = hashlib.sha256(data_str.encode())
        
        return hash_obj.hexdigest()
    
    async def _initialize_vault(self) -> None:
        """初始化Vault客户端并进行认证"""
        if not self._vault_config:
            if self._mode == "production":
                raise ValueError("Vault config is required in production mode")
            self.logger.warning("Vault config not provided. Skipping Vault initialization.")
            return

        try:
            # 安全日志：避免记录完整URL
            self.logger.info(f"Initializing Vault client")
            self._vault_client = hvac.Client(
                url=self._vault_config.vault_url,
                token=self._vault_config.vault_token,
                namespace=self._vault_config.vault_namespace,
                verify=self._vault_config.ca_cert_path if self._vault_config.ca_cert_path else False,
            )

            if self._vault_client.is_authenticated():
                self.logger.info("Vault client authenticated successfully.")
            else:
                error_msg = "Vault client authentication failed. Check token or configuration."
                self.logger.error(error_msg)
                # 在生产模式下，Vault认证失败应该抛出异常
                if self._mode == "production":
                    raise RuntimeError(error_msg)
        
        except Exception as e:
            self.logger.error(f"Failed to initialize Vault client: {e}")
            self._vault_client = None # 初始化失败，重置客户端
            raise
    
    def _validate_protocol(self, config: Union[EncryptionConfig, SignatureConfig]) -> None:
        """
        协议一致性验证 - 设计文档5.2要求
        
        检查算法是否支持当前环境配置
        验证密钥ID格式是否符合规范
        """
        try:
            # 验证密钥ID格式 (后端系统要求15位以上)
            key_id = getattr(config.key_config, 'key_id', '')
            if len(key_id) < 15:
                error_msg = "Key ID must be at least 15 characters"
                self.logger.error(error_msg)
                raise ValueError(error_msg)
            
            # 验证算法支持
            algorithm = config.algorithm
            if isinstance(config, EncryptionConfig):
                if algorithm not in EncryptionAlgorithm:
                    error_msg = f"Unsupported encryption algorithm: {algorithm}"
                    self.logger.error(error_msg)
                    raise ValueError(error_msg)
            elif isinstance(config, SignatureConfig):
                if algorithm not in SignatureAlgorithm:
                    error_msg = f"Unsupported signature algorithm: {algorithm}"
                    self.logger.error(error_msg)
                    raise ValueError(error_msg)

            # 特殊的环境合规检查
            if getattr(self, '_mode', 'production') == 'cn-production':
                if algorithm in ["SM2", "SM3", "SM4"]:
                    self.logger.debug(
                        f"Accepted government-compliant algorithm: {algorithm}"
                    )
                else:
                    warning_msg = "Non-compliant algorithm in restricted environment"
                    self.logger.warning(warning_msg)
                    # 在受限环境中仅允许国密算法
                    raise ValueError(warning_msg)

            self.logger.debug("Protocol consistency validated")

        except Exception as e:
            self.logger.error(f"Protocol validation failed: {e}")
            raise
                
    async def _encrypt(self, request: CryptoRequest) -> str:
        """加密数据"""
        if not request.encryption_config:
            raise ValueError("Encryption config is required for encrypt operation")
        
        config = request.encryption_config
        
        # 算法支持检查
        supported_algorithms = [
            EncryptionAlgorithm.AES_GCM,
            EncryptionAlgorithm.SM4,
            EncryptionAlgorithm.DES3
        ]
        if config.algorithm not in supported_algorithms:
            raise NotImplementedError(f"Algorithm {config.algorithm} not supported")

        # 安全日志：避免记录完整配置
        self.logger.info(
            f"Encrypting data",
            operation="encrypt",
            algorithm=config.algorithm.value,
            key_id=config.key_config.key_id
        )
        
        # 协议一致性验证 (设计文档5.2)
        self._validate_protocol(config)
        
        key = await self._get_key_from_vault(
            config.key_config.key_id,
            config.key_config.vault_path
        )
        
        # 根据不同算法使用不同的加密实现
        if config.algorithm == EncryptionAlgorithm.AES_GCM:
            nonce = os.urandom(12)
            cipher = AESGCM(key)
            ciphertext = cipher.encrypt(nonce, request.data.encode(), None)
            result = nonce + ciphertext
            return base64.b64encode(result).decode()
        
        elif config.algorithm == EncryptionAlgorithm.SM4:
            try:
                # SM4-GCM实现占位
                # 实际项目需添加pycryptodomex等库实现
                from Crypto.Cipher import AES  # 实际应使用国密库
                # 使用AES模拟SM4加密
                cipher = AES.new(key[:16], AES.MODE_GCM)
                ciphertext, tag = cipher.encrypt_and_digest(request.data.encode())
                result = cipher.nonce + ciphertext + tag
                return base64.b64encode(result).decode()
            except ImportError:
                # 开发环境回退
                if self._mode == "dev":
                    return base64.b64encode(f"SIMULATED_SM4:{request.data}".encode()).decode()
                raise NotImplementedError("SM4 requires Crypto library in production")
        
        elif config.algorithm == EncryptionAlgorithm.DES3:
            try:
                # 3DES实现占位
                from Crypto.Cipher import DES3
                cipher = DES3.new(key, DES3.MODE_ECB)
                # 填充数据为8字节倍数
                data = request.data.encode()
                pad_len = 8 - (len(data) % 8)
                data += pad_len * chr(pad_len).encode()
                ciphertext = cipher.encrypt(data)
                return base64.b64encode(ciphertext).decode()
            except ImportError:
                if self._mode == "dev":
                    return base64.b64encode(f"SIMULATED_3DES:{request.data}".encode()).decode()
                raise NotImplementedError("3DES requires Crypto library in production")
        
    async def _sign(self, request: CryptoRequest) -> str:
        """签名数据"""
        if not request.signature_config:
            raise ValueError("Signature config is required for sign operation")
        
        config = request.signature_config
        
        # 支持算法检查
        supported_algorithms = [
            SignatureAlgorithm.RSA_PSS,
            SignatureAlgorithm.RSA_PKCS1,  # RSA PKCS1实现
            SignatureAlgorithm.SM2
        ]
        if config.algorithm not in supported_algorithms:
            raise NotImplementedError(f"Algorithm {config.algorithm} not supported")
            
        # 安全日志：避免记录完整配置
        self.logger.info(
            f"Signing data",
            operation="sign",
            algorithm=config.algorithm.value,
            key_id=config.key_config.key_id
        )
        
        # 协议一致性验证 (设计文档5.2)
        self._validate_protocol(config)
        
        private_key = await self._get_key_from_vault(
            config.key_config.key_id,
            config.key_config.vault_path
        )
        
        # 根据不同算法使用不同的签名实现
        if config.algorithm in [SignatureAlgorithm.RSA_PSS, SignatureAlgorithm.RSA_PKCS1]:
            # RSA 系列签名
            if config.algorithm == SignatureAlgorithm.RSA_PSS:
                pad = padding.PSS(
                    mgf=padding.MGF1(hashes.SHA256()),
                    salt_length=padding.PSS.MAX_LENGTH
                )
            else:  # RSA_PKCS1
                pad = padding.PKCS1v15()
            
            signature = private_key.sign(
                request.data.encode(),
                pad,
                hashes.SHA256()  # 可根据需求配置不同哈希
            )
            return base64.b64encode(signature).decode()
        
        elif config.algorithm == SignatureAlgorithm.SM2:
            try:
                # SM2签名实现
                # 使用gmssl库实现国密SM2算法
                from gmssl import sm2, func  # type: ignore

                # 假设private_key已经是SM2私钥对象或私钥字节
                if isinstance(private_key, bytes):
                    # 如果是字节形式的私钥，直接使用
                    private_key_bytes = private_key
                else:
                    # 如果是其他格式，需要转换
                    private_key_bytes = private_key

                # 使用SM2进行签名
                sig = sm2.CryptSM2(private_key=private_key_bytes, public_key=None).sign(
                    request.data.encode(),
                    func.random_hex(64)  # 随机数
                )
                return base64.b64encode(bytes.fromhex(sig)).decode()
            except ImportError:
                if self._mode == "dev":
                    return "SIMULATED_SM2_SIGNATURE"
                raise NotImplementedError("SM2 requires gmssl library in production")
            except Exception as e:
                self.logger.error(f"SM2 signature failed: {e}")
                if self._mode == "dev":
                    return "SIMULATED_SM2_SIGNATURE_ERROR"
                raise
        
    async def _hash(self, request: CryptoRequest) -> str:
        """计算哈希"""
        if not request.hash_algorithm:
            raise ValueError("Hash algorithm is required for hash operation")
        
        algorithm = request.hash_algorithm
        
        # 安全日志：避免记录完整配置
        self.logger.info(
            f"Hashing data",
            operation="hash",
            algorithm=algorithm.value
        )
        
        # 根据不同算法使用不同的哈希实现
        import hashlib
        if algorithm == HashAlgorithm.SHA256:
            h = hashlib.sha256()
            h.update(request.data.encode())
            return h.hexdigest()

        elif algorithm == HashAlgorithm.SHA1:
            h = hashlib.sha1()
            h.update(request.data.encode())
            return h.hexdigest()

        elif algorithm == HashAlgorithm.SM3:
            try:
                # SM3实现占位
                # 实际项目需安装国密库
                # 使用SHA256模拟，真实项目需要替换
                h = hashlib.sha256()
                h.update(request.data.encode())
                return h.hexdigest() + "_SM3_SIMULATED"
            except:
                if self._mode == "dev":
                    return "SIMULATED_SM3_HASH"
                raise NotImplementedError("SM3 requires specialized library in production")
                
        elif algorithm == HashAlgorithm.MD5:
            h = hashlib.md5()
            h.update(request.data.encode())
            return h.hexdigest()
            
        else:
            raise NotImplementedError(f"Unsupported hash algorithm: {algorithm}")

    async def _validate_algorithms(self) -> None:
        """验证支持的算法"""
        try:
            self.logger.info("Validating algorithm support")
            
            # 添加实际支持的算法列表
            implemented_encryption = [alg.name for alg in [
                EncryptionAlgorithm.AES_GCM, 
                EncryptionAlgorithm.SM4
            ]]
            self.logger.info(
                f"Implemented encryption algorithms: {implemented_encryption}"
            )
            
            implemented_signature = [alg.name for alg in [
                SignatureAlgorithm.RSA_PSS,
                SignatureAlgorithm.SM2
            ]]
            self.logger.info(
                f"Implemented signature algorithms: {implemented_signature}"
            )
            
            implemented_hash = [alg.name for alg in HashAlgorithm]
            self.logger.info(
                f"Implemented hash algorithms: {implemented_hash}"
            )
            
            self.logger.info("Algorithm validation completed")
            
        except Exception as e:
            self.logger.error(f"Algorithm validation failed: {e}")
            raise
    
    async def _get_key_from_vault(self, key_id: str, vault_path: Optional[str]) -> bytes:
        """
        从Vault获取密钥，生产环境禁用本地回退
        """
        # 生产模式下Vault是必需的
        if self._mode == "production" and (not self._vault_client or not self._vault_client.is_authenticated()):
            error_msg = "Vault is required in production mode"
            self.logger.error(error_msg)
            raise RuntimeError(error_msg)
            
        # 优先从缓存获取
        if key_id in self._key_cache and not self._is_cache_expired(key_id):
            self.logger.debug(f"Using cached key for key_id: {key_id}")
            return self._key_cache[key_id]['key_data']

        # 如果Vault已配置并认证通过
        if self._vault_client and self._vault_client.is_authenticated():
            try:
                # 安全日志：只记录key_id，不记录完整路径
                self.logger.info(f"Fetching key from Vault for key_id: {key_id}")
                
                path = vault_path or f"{self._vault_config.engine_path}/data/{key_id}"
                # 从Vault读取密钥
                secret_response = self._vault_client.secrets.kv.v2.read_secret_version(path=path)
                key_data_str = secret_response['data']['data']['key']
                key_data = base64.b64decode(key_data_str)

                # 缓存密钥
                self._cache_key(key_id, key_data)
                return key_data

            except Exception as e:
                self.logger.error(f"Failed to fetch key from Vault for key_id {key_id}: {e}")
                raise

        # 开发模式下允许临时回退
        if self._mode == "dev":
            self.logger.warning(f"Vault not available in dev mode. Using temp key for key_id: {key_id}")

            # 扩展以支持RSA密钥对生成
            # 检查是否需要非对称密钥（RSA或签名相关）
            if ("rsa" in key_id.lower() or "sign" in key_id.lower() or
                "private" in key_id.lower() or "public" in key_id.lower()):
                private_key = rsa.generate_private_key(
                    public_exponent=65537,
                    key_size=2048,
                )
                if "public" in key_id.lower():
                    key_data = private_key.public_key()
                else:
                    # 默认返回私钥（用于签名）
                    key_data = private_key
            else: # 原有的对称密钥逻辑
                from hashlib import sha256
                if "aes-256" in key_id.lower():
                    key_data = sha256(key_id.encode()).digest()
                elif "aes-128" in key_id.lower():
                    key_data = sha256(key_id.encode()).digest()[:16]
                else:
                    key_data = sha256(b"default-secret-key-for-testing").digest()

            self._cache_key(key_id, key_data)
            return key_data
        else:
            error_msg = f"Key retrieval failed for {key_id} in {self._mode} mode"
            self.logger.error(error_msg)
            raise RuntimeError(error_msg)

    def _cache_key(self, key_id: str, key_data: Union[bytes, rsa.RSAPrivateKey, rsa.RSAPublicKey]) -> None:
        """缓存密钥"""
        if len(self._key_cache) >= self._cache_max_size:
            self._cleanup_expired_cache()
            if len(self._key_cache) >= self._cache_max_size:
                # 如果清理后仍然满，则随机移除一个
                self._key_cache.pop(next(iter(self._key_cache)))

        self._key_cache[key_id] = {
            'key_data': key_data,
            'timestamp': datetime.now()
        }
        self.logger.debug(f"Cached key for key_id: {key_id}")

    def _is_cache_expired(self, key_id: str) -> bool:
        """检查缓存是否过期"""
        if key_id not in self._key_cache:
            return True
        
        cache_entry = self._key_cache[key_id]
        age = datetime.now() - cache_entry['timestamp']
        return age > timedelta(minutes=self._cache_ttl_minutes)

    def _cleanup_expired_cache(self) -> None:
        """清理过期的缓存条目"""
        expired_keys = [
            key for key, value in self._key_cache.items()
            if (datetime.now() - value['timestamp']) > timedelta(minutes=self._cache_ttl_minutes)
        ]
        for key in expired_keys:
            del self._key_cache[key]
        self.logger.info(f"Cleaned up {len(expired_keys)} expired cache entries.")