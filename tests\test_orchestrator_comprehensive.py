"""
TestGenius Orchestrator 综合测试套件

测试覆盖：
1. 核心功能测试 - 会话管理、测试用例生成、脚本生成
2. 边界条件测试 - 参数验证、资源限制、并发处理
3. 异常情况测试 - 错误处理、故障恢复、降级机制
4. 集成测试 - 模块间协作、端到端流程
5. 性能测试 - 响应时间、并发能力、资源使用
"""

import pytest
import asyncio
import time
from typing import Dict, List, Any
from uuid import UUID, uuid4
from unittest.mock import AsyncMock, MagicMock, patch

from src.orchestrator.service import OrchestratorService
from src.orchestrator.models import (
    SessionContext,
    TestCaseGenerationRequest,
    TestCaseGenerationResponse,
    ScriptGenerationRequest,
    ScriptGenerationResponse,
    ExecutionRequest,
    ExecutionResponse,
    ExecutionState,
    TaskStatus,
    TestCaseDefinition,
)
from src.test_case.models import TestCaseType, TestPriority
from src.ai.llm_client import LLMProvider


class TestOrchestratorCore:
    """Orchestrator 核心功能测试"""
    
    @pytest.fixture
    async def orchestrator(self):
        """创建并初始化 Orchestrator 服务"""
        service = OrchestratorService()
        await service.initialize()
        yield service
        await service.cleanup()
    
    @pytest.mark.asyncio
    async def test_service_initialization(self, orchestrator):
        """测试服务初始化"""
        assert orchestrator._initialized is True
        assert orchestrator._ready is True
        assert orchestrator._test_case_generator is not None
        assert isinstance(orchestrator._sessions, dict)
        assert isinstance(orchestrator._tasks, dict)
        assert isinstance(orchestrator._executions, dict)
    
    @pytest.mark.asyncio
    async def test_session_creation(self, orchestrator):
        """测试会话创建"""
        session = await orchestrator.create_session()
        
        assert isinstance(session, SessionContext)
        assert isinstance(session.session_id, UUID)
        assert session.created_at is not None
        assert session.status == "active"
        assert session.session_id in orchestrator._sessions
    
    @pytest.mark.asyncio
    async def test_session_retrieval(self, orchestrator):
        """测试会话检索"""
        # 创建会话
        session = await orchestrator.create_session()
        session_id = session.session_id
        
        # 检索会话
        retrieved_session = await orchestrator.get_session(session_id)
        
        assert retrieved_session is not None
        assert retrieved_session.session_id == session_id
        assert retrieved_session.status == "active"
    
    @pytest.mark.asyncio
    async def test_session_not_found(self, orchestrator):
        """测试会话不存在的情况"""
        non_existent_id = uuid4()

        # 获取不存在的会话应该返回None，而不是抛出异常
        result = await orchestrator.get_session(non_existent_id)
        assert result is None

    @pytest.mark.asyncio
    async def test_test_case_generation_basic(self, orchestrator):
        """测试基本测试用例生成"""
        # 创建会话
        session = await orchestrator.create_session()

        # 创建测试用例生成请求 - 使用正确的字段名
        request = TestCaseGenerationRequest(
            session_id=session.session_id,
            requirement_text="用户登录功能测试：支持用户名密码登录，验证输入格式，处理登录失败情况",
            context={
                "domain": "用户管理",
                "system_type": "web_application",
                "technology_stack": ["Python", "FastAPI"]
            },
            generation_options={
                "max_test_cases": 5,
                "include_boundary_tests": True,
                "include_exception_tests": True
            },
            crypto_enabled=False,
            target_coverage=0.8
        )

        # 生成测试用例
        response = await orchestrator.generate_test_cases(request)

        assert isinstance(response, TestCaseGenerationResponse)
        assert response.session_id == session.session_id
        assert response.status == TaskStatus.COMPLETED
        assert len(response.test_cases) > 0
        assert all(isinstance(tc, TestCaseDefinition) for tc in response.test_cases)
    
    @pytest.mark.asyncio
    async def test_test_case_generation_with_crypto(self, orchestrator):
        """测试带加密需求的测试用例生成"""
        session = await orchestrator.create_session()

        request = TestCaseGenerationRequest(
            session_id=session.session_id,
            requirement_text="支付接口测试，需要RSA签名和AES加密，确保数据传输安全",
            context={
                "domain": "支付系统",
                "system_type": "api",
                "technology_stack": ["Python", "FastAPI", "PostgreSQL"],
                "security_requirements": {
                    "encryption": True,
                    "signature": True,
                    "authentication": "JWT"
                }
            },
            generation_options={
                "max_test_cases": 3,
                "include_security_tests": True,
                "include_boundary_tests": True
            },
            crypto_enabled=True,
            target_coverage=0.9
        )

        response = await orchestrator.generate_test_cases(request)

        assert response.status == TaskStatus.COMPLETED
        assert len(response.test_cases) > 0

        # 检查是否包含加密相关的测试用例
        crypto_test_cases = [
            tc for tc in response.test_cases
            if tc.crypto_requirements is not None or tc.signature_requirements is not None
        ]
        assert len(crypto_test_cases) > 0


class TestOrchestratorBoundaryConditions:
    """边界条件测试"""
    
    @pytest.fixture
    async def orchestrator(self):
        service = OrchestratorService()
        await service.initialize()
        yield service
        await service.cleanup()
    
    @pytest.mark.asyncio
    async def test_empty_requirements(self, orchestrator):
        """测试空需求输入"""
        session = await orchestrator.create_session()

        request = TestCaseGenerationRequest(
            session_id=session.session_id,
            requirement_text="",  # 空需求
            context={"domain": "通用"},
            generation_options={"max_test_cases": 1}
        )

        response = await orchestrator.generate_test_cases(request)

        # 应该能处理空需求，但可能生成通用测试用例
        assert response.status == TaskStatus.COMPLETED
        assert len(response.test_cases) > 0  # 应该生成默认测试用例

    @pytest.mark.asyncio
    async def test_large_requirements(self, orchestrator):
        """测试大量需求文本"""
        session = await orchestrator.create_session()

        # 创建一个很长的需求文本
        large_requirements = "测试需求：" + "这是一个复杂的业务需求，包含多个功能模块和业务流程。" * 100

        request = TestCaseGenerationRequest(
            session_id=session.session_id,
            requirement_text=large_requirements,
            context={"domain": "复杂系统"},
            generation_options={"max_test_cases": 3}
        )

        response = await orchestrator.generate_test_cases(request)

        # 应该能处理大文本，但可能有性能影响
        assert response.status == TaskStatus.COMPLETED
        assert len(response.test_cases) > 0
    
    @pytest.mark.asyncio
    async def test_maximum_test_case_count(self, orchestrator):
        """测试最大测试用例数量限制"""
        session = await orchestrator.create_session()

        request = TestCaseGenerationRequest(
            session_id=session.session_id,
            requirement_text="基本功能测试，包含多个业务场景",
            context={"domain": "通用系统"},
            generation_options={"max_test_cases": 100}  # 请求大量测试用例
        )

        response = await orchestrator.generate_test_cases(request)

        # 应该有合理的限制
        assert response.status == TaskStatus.COMPLETED
        assert len(response.test_cases) <= 50  # 假设最大限制是50

    @pytest.mark.asyncio
    async def test_invalid_session_id(self, orchestrator):
        """测试无效的会话ID"""
        invalid_session_id = uuid4()

        request = TestCaseGenerationRequest(
            session_id=invalid_session_id,
            requirement_text="基本功能测试",
            context={"domain": "测试系统"}
        )

        # 应该抛出会话不存在的异常
        with pytest.raises(ValueError, match="Session .* not found"):
            await orchestrator.generate_test_cases(request)


class TestOrchestratorErrorHandling:
    """异常处理测试"""

    @pytest.fixture
    async def orchestrator(self):
        service = OrchestratorService()
        await service.initialize()
        yield service
        await service.cleanup()

    @pytest.mark.asyncio
    async def test_ai_service_failure(self, orchestrator):
        """测试AI服务故障时的处理"""
        session = await orchestrator.create_session()

        # 模拟AI服务故障
        with patch.object(orchestrator._test_case_generator, 'generate_test_cases') as mock_generate:
            mock_generate.side_effect = Exception("AI service unavailable")

            request = TestCaseGenerationRequest(
                session_id=session.session_id,
                requirement_text="测试需求",
                context={"domain": "测试系统"}
            )

            # 应该抛出异常，因为当前实现没有错误恢复机制
            with pytest.raises(Exception, match="AI service unavailable"):
                await orchestrator.generate_test_cases(request)

    @pytest.mark.asyncio
    async def test_concurrent_session_access(self, orchestrator):
        """测试并发会话访问"""
        # 创建会话
        session = await orchestrator.create_session()
        session_id = session.session_id

        # 并发访问同一会话
        async def access_session():
            return await orchestrator.get_session(session_id)

        tasks = [access_session() for _ in range(10)]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 所有访问都应该成功
        for result in results:
            assert not isinstance(result, Exception)
            assert result.session_id == session_id

    @pytest.mark.asyncio
    async def test_memory_cleanup(self, orchestrator):
        """测试内存清理机制"""
        initial_session_count = len(orchestrator._sessions)

        # 创建多个会话
        sessions = []
        for _ in range(10):
            session = await orchestrator.create_session()
            sessions.append(session)

        assert len(orchestrator._sessions) == initial_session_count + 10

        # 模拟会话过期清理（如果实现了的话）
        # 这里只是验证会话确实被存储了
        for session in sessions:
            stored_session = await orchestrator.get_session(session.session_id)
            assert stored_session is not None


class TestOrchestratorIntegration:
    """集成测试"""

    @pytest.fixture
    async def orchestrator(self):
        service = OrchestratorService()
        await service.initialize()
        yield service
        await service.cleanup()

    @pytest.mark.asyncio
    async def test_end_to_end_workflow(self, orchestrator):
        """测试端到端工作流程"""
        # 1. 创建会话
        session = await orchestrator.create_session()

        # 2. 生成测试用例
        tc_request = TestCaseGenerationRequest(
            session_id=session.session_id,
            requirement_text="用户注册功能测试：支持邮箱注册，验证输入格式，处理重复注册",
            context={
                "domain": "用户管理",
                "system_type": "web_application"
            },
            generation_options={
                "max_test_cases": 3,
                "include_boundary_tests": True
            }
        )

        tc_response = await orchestrator.generate_test_cases(tc_request)
        assert tc_response.status == TaskStatus.COMPLETED
        assert len(tc_response.test_cases) > 0

        # 3. 生成脚本
        test_case_ids = [tc.test_case_id for tc in tc_response.test_cases]

        script_request = ScriptGenerationRequest(
            session_id=session.session_id,
            test_case_ids=test_case_ids,
            target_language="python",
            target_framework="pytest"
        )

        script_response = await orchestrator.generate_scripts(script_request)
        assert isinstance(script_response, ScriptGenerationResponse)
        assert script_response.status == TaskStatus.COMPLETED
        assert len(script_response.scripts) > 0

        # 4. 启动执行（模拟）
        script_ids = [script.script_id for script in script_response.scripts]

        exec_request = ExecutionRequest(
            session_id=session.session_id,
            script_ids=script_ids,
            environment="test",
            parallel=True
        )

        exec_response = await orchestrator.start_execution(exec_request)
        assert isinstance(exec_response, ExecutionResponse)
        assert exec_response.execution_id is not None
        assert exec_response.status == ExecutionState.QUEUED

    @pytest.mark.asyncio
    async def test_workflow_with_crypto(self, orchestrator):
        """测试包含加密的完整工作流程"""
        session = await orchestrator.create_session()

        # 生成包含加密需求的测试用例
        tc_request = TestCaseGenerationRequest(
            session_id=session.session_id,
            requirement_text="API接口测试，需要RSA签名验证和AES数据加密",
            context={
                "domain": "安全API",
                "system_type": "api",
                "security_requirements": {
                    "encryption": True,
                    "signature": True
                }
            },
            generation_options={
                "max_test_cases": 2,
                "include_security_tests": True
            },
            crypto_enabled=True
        )

        tc_response = await orchestrator.generate_test_cases(tc_request)
        assert tc_response.status == TaskStatus.COMPLETED

        # 检查生成的测试用例是否包含加密需求
        has_crypto = any(
            tc.crypto_requirements is not None or tc.signature_requirements is not None
            for tc in tc_response.test_cases
        )
        assert has_crypto is True


class TestOrchestratorPerformance:
    """性能测试"""

    @pytest.fixture
    async def orchestrator(self):
        service = OrchestratorService()
        await service.initialize()
        yield service
        await service.cleanup()

    @pytest.mark.asyncio
    async def test_session_creation_performance(self, orchestrator):
        """测试会话创建性能"""
        start_time = time.time()

        # 创建多个会话
        sessions = []
        for _ in range(50):
            session = await orchestrator.create_session()
            sessions.append(session)

        end_time = time.time()
        duration = end_time - start_time

        # 验证性能要求（每个会话创建应该在合理时间内）
        avg_time_per_session = duration / 50
        assert avg_time_per_session < 0.1  # 每个会话创建应该少于100ms

        print(f"Created 50 sessions in {duration:.2f}s (avg: {avg_time_per_session:.3f}s per session)")

    @pytest.mark.asyncio
    async def test_concurrent_test_case_generation(self, orchestrator):
        """测试并发测试用例生成"""
        session = await orchestrator.create_session()

        async def generate_test_cases():
            request = TestCaseGenerationRequest(
                session_id=session.session_id,
                requirement_text="并发测试需求：基本功能验证",
                context={"domain": "并发测试"},
                generation_options={"max_test_cases": 1}
            )
            return await orchestrator.generate_test_cases(request)

        start_time = time.time()

        # 并发生成测试用例
        tasks = [generate_test_cases() for _ in range(5)]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        end_time = time.time()
        duration = end_time - start_time

        # 验证所有请求都成功
        successful_results = [
            r for r in results
            if not isinstance(r, Exception) and r.status == TaskStatus.COMPLETED
        ]
        assert len(successful_results) == 5

        print(f"Concurrent test case generation: {duration:.2f}s for 5 requests")

        # 并发处理应该比串行处理快
        assert duration < 10.0  # 5个请求应该在10秒内完成


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])
