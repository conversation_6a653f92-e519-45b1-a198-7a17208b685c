"""
数据库管理模块

负责数据持久化、缓存管理和数据访问
"""

import asyncio
import json
import hashlib
from typing import Dict, List, Optional, Any, Union
from uuid import UUID, uuid4
from datetime import datetime, timedelta
from enum import Enum
from pathlib import Path
import sqlite3
import aiosqlite

from src.common.logger import get_logger
from src.common.config import Settings


class DatabaseType(str, Enum):
    """数据库类型"""
    SQLITE = "sqlite"
    POSTGRESQL = "postgresql"
    REDIS = "redis"


class CacheStrategy(str, Enum):
    """缓存策略"""
    LRU = "lru"
    LFU = "lfu"
    TTL = "ttl"
    WRITE_THROUGH = "write_through"
    WRITE_BACK = "write_back"


class DataRecord:
    """数据记录基类"""
    
    def __init__(
        self,
        record_id: UUID,
        record_type: str,
        data: Dict[str, Any],
        created_at: Optional[datetime] = None,
        updated_at: Optional[datetime] = None
    ):
        self.record_id = record_id
        self.record_type = record_type
        self.data = data
        self.created_at = created_at or datetime.now()
        self.updated_at = updated_at or datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "record_id": str(self.record_id),
            "record_type": self.record_type,
            "data": self.data,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DataRecord':
        """从字典创建记录"""
        return cls(
            record_id=UUID(data["record_id"]),
            record_type=data["record_type"],
            data=data["data"],
            created_at=datetime.fromisoformat(data["created_at"]),
            updated_at=datetime.fromisoformat(data["updated_at"])
        )


class CacheItem:
    """缓存项"""
    
    def __init__(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None,
        access_count: int = 0
    ):
        self.key = key
        self.value = value
        self.created_at = datetime.now()
        self.last_accessed = datetime.now()
        self.access_count = access_count
        self.ttl = ttl
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.ttl is None:
            return False
        return datetime.now() > self.created_at + timedelta(seconds=self.ttl)
    
    def access(self) -> Any:
        """访问缓存项"""
        self.last_accessed = datetime.now()
        self.access_count += 1
        return self.value


class DatabaseManager:
    """
    数据库管理器
    
    负责数据持久化、缓存管理和数据访问
    """
    
    def __init__(self, settings: Optional[Settings] = None):
        self.logger = get_logger(__name__)
        self.settings = settings or Settings()
        
        # 数据库连接
        self._db_connection: Optional[Any] = None
        self._db_type = DatabaseType.SQLITE  # 默认使用SQLite
        self._db_path = "testgenius.db"
        
        # 内存存储（用于开发和测试）
        self._memory_store: Dict[str, Dict[UUID, DataRecord]] = {
            "sessions": {},
            "test_cases": {},
            "executions": {},
            "feedback": {},
            "users": {},
            "metrics": {},
            "alerts": {}
        }
        
        # 缓存管理
        self._cache: Dict[str, CacheItem] = {}
        self._cache_strategy = CacheStrategy.LRU
        self._max_cache_size = 1000
        self._default_cache_ttl = 3600  # 1小时
        
        # 统计信息
        self._total_reads = 0
        self._total_writes = 0
        self._cache_hits = 0
        self._cache_misses = 0
        
        self._initialized = False
    
    async def initialize(self) -> None:
        """初始化数据库管理器"""
        try:
            self.logger.info("Initializing DatabaseManager")
            
            # 根据配置选择数据库类型
            if hasattr(self.settings, 'database') and hasattr(self.settings.database, 'use_postgresql'):
                if self.settings.database.use_postgresql:
                    await self._initialize_postgresql()
                else:
                    await self._initialize_sqlite()
            else:
                await self._initialize_sqlite()
            
            # 创建表结构
            await self._create_tables()
            
            # 启动缓存清理任务
            asyncio.create_task(self._cache_cleanup_task())
            
            self._initialized = True
            self.logger.info("DatabaseManager initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize DatabaseManager: {e}")
            raise
    
    async def cleanup(self) -> None:
        """清理资源"""
        try:
            self.logger.info("Cleaning up DatabaseManager")
            
            # 关闭数据库连接
            if self._db_connection:
                if self._db_type == DatabaseType.SQLITE:
                    await self._db_connection.close()
                # PostgreSQL 连接池清理在这里添加
            
            # 清理内存存储
            self._memory_store.clear()
            self._cache.clear()
            
            self._initialized = False
            self.logger.info("DatabaseManager cleaned up successfully")
            
        except Exception as e:
            self.logger.error(f"Error during DatabaseManager cleanup: {e}")
    
    def is_ready(self) -> bool:
        """检查数据库管理器是否准备就绪"""
        return self._initialized
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            health_status = {
                "status": "healthy",
                "initialized": self._initialized,
                "database_type": self._db_type.value,
                "connection_status": "connected" if self._db_connection else "disconnected",
                "cache_status": {
                    "size": len(self._cache),
                    "hit_rate": self._get_cache_hit_rate()
                },
                "memory_store_status": {
                    "total_records": sum(len(records) for records in self._memory_store.items()),
                    "record_types": list(self._memory_store.keys())
                }
            }
            
            # 如果有数据库连接，测试连接
            if self._db_connection and self._db_type == DatabaseType.SQLITE:
                try:
                    cursor = await self._db_connection.execute("SELECT 1")
                    await cursor.fetchone()
                    health_status["database_test"] = "success"
                except Exception as e:
                    health_status["status"] = "degraded"
                    health_status["database_test"] = f"failed: {str(e)}"
            
            return health_status
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "initialized": self._initialized
            }
    
    def _get_cache_hit_rate(self) -> float:
        """计算缓存命中率"""
        total_requests = self._cache_hits + self._cache_misses
        if total_requests == 0:
            return 0.0
        return round((self._cache_hits / total_requests) * 100, 2)
    
    async def backup_data(self, backup_path: Optional[str] = None) -> str:
        """备份数据"""
        try:
            backup_file = backup_path or f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            backup_data = {
                "timestamp": datetime.now().isoformat(),
                "database_type": self._db_type.value,
                "memory_store": {}
            }
            
            # 备份内存存储数据
            for record_type, records in self._memory_store.items():
                backup_data["memory_store"][record_type] = {}
                for record_id, record in records.items():
                    backup_data["memory_store"][record_type][str(record_id)] = record.to_dict()
            
            # 写入备份文件
            import json
            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Data backed up to: {backup_file}")
            return backup_file
            
        except Exception as e:
            self.logger.error(f"Failed to backup data: {e}")
            raise
    
    async def restore_data(self, backup_path: str) -> bool:
        """恢复数据"""
        try:
            import json
            with open(backup_path, 'r', encoding='utf-8') as f:
                backup_data = json.load(f)
            
            # 恢复内存存储数据
            for record_type, records in backup_data.get("memory_store", {}).items():
                if record_type not in self._memory_store:
                    self._memory_store[record_type] = {}
                
                for record_id_str, record_data in records.items():
                    record = DataRecord.from_dict(record_data)
                    self._memory_store[record_type][record.record_id] = record
            
            self.logger.info(f"Data restored from: {backup_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to restore data: {e}")
            return False
    
    async def store_session(self, session_data: Dict[str, Any]) -> UUID:
        """存储会话数据"""
        try:
            session_id = UUID(session_data.get("session_id")) if session_data.get("session_id") else uuid4()
            
            record = DataRecord(
                record_id=session_id,
                record_type="session",
                data=session_data
            )
            
            await self._store_record("sessions", record)
            self.logger.info(f"Session stored: {session_id}")
            return session_id
            
        except Exception as e:
            self.logger.error(f"Failed to store session: {e}")
            raise
    
    async def get_session(self, session_id: UUID) -> Optional[Dict[str, Any]]:
        """获取会话数据"""
        try:
            record = await self._get_record("sessions", session_id)
            if record:
                return record.data
            return None
            
        except Exception as e:
            self.logger.error(f"Failed to get session {session_id}: {e}")
            return None
    
    async def update_session(self, session_id: UUID, updates: Dict[str, Any]) -> bool:
        """更新会话数据"""
        try:
            record = await self._get_record("sessions", session_id)
            if not record:
                return False
            
            record.data.update(updates)
            record.updated_at = datetime.now()
            
            await self._store_record("sessions", record)
            self.logger.info(f"Session updated: {session_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to update session {session_id}: {e}")
            return False
    
    async def store_test_case(self, test_case_data: Dict[str, Any]) -> UUID:
        """存储测试用例"""
        try:
            test_case_id = UUID(test_case_data.get("test_case_id")) if test_case_data.get("test_case_id") else uuid4()
            
            record = DataRecord(
                record_id=test_case_id,
                record_type="test_case",
                data=test_case_data
            )
            
            await self._store_record("test_cases", record)
            self.logger.info(f"Test case stored: {test_case_id}")
            return test_case_id
            
        except Exception as e:
            self.logger.error(f"Failed to store test case: {e}")
            raise
    
    async def get_test_case(self, test_case_id: UUID) -> Optional[Dict[str, Any]]:
        """获取测试用例"""
        try:
            record = await self._get_record("test_cases", test_case_id)
            if record:
                return record.data
            return None
            
        except Exception as e:
            self.logger.error(f"Failed to get test case {test_case_id}: {e}")
            return None
    
    async def store_execution_record(self, execution_data: Dict[str, Any]) -> UUID:
        """存储执行记录"""
        try:
            execution_id = UUID(execution_data.get("execution_id")) if execution_data.get("execution_id") else uuid4()
            
            record = DataRecord(
                record_id=execution_id,
                record_type="execution",
                data=execution_data
            )
            
            await self._store_record("executions", record)
            self.logger.info(f"Execution record stored: {execution_id}")
            return execution_id
            
        except Exception as e:
            self.logger.error(f"Failed to store execution record: {e}")
            raise
    
    async def get_execution_record(self, execution_id: UUID) -> Optional[Dict[str, Any]]:
        """获取执行记录"""
        try:
            record = await self._get_record("executions", execution_id)
            if record:
                return record.data
            return None
            
        except Exception as e:
            self.logger.error(f"Failed to get execution record {execution_id}: {e}")
            return None
    
    async def store_feedback(self, feedback_data: Dict[str, Any]) -> UUID:
        """存储反馈数据"""
        try:
            feedback_id = UUID(feedback_data.get("feedback_id")) if feedback_data.get("feedback_id") else uuid4()
            
            record = DataRecord(
                record_id=feedback_id,
                record_type="feedback",
                data=feedback_data
            )
            
            await self._store_record("feedback", record)
            self.logger.info(f"Feedback stored: {feedback_id}")
            return feedback_id
            
        except Exception as e:
            self.logger.error(f"Failed to store feedback: {e}")
            raise
    
    async def get_feedback(self, feedback_id: UUID) -> Optional[Dict[str, Any]]:
        """获取反馈数据"""
        try:
            record = await self._get_record("feedback", feedback_id)
            if record:
                return record.data
            return None
            
        except Exception as e:
            self.logger.error(f"Failed to get feedback {feedback_id}: {e}")
            return None
    
    async def store_user_data(self, user_data: Dict[str, Any]) -> UUID:
        """存储用户数据"""
        try:
            user_id = UUID(user_data.get("user_id")) if user_data.get("user_id") else uuid4()
            
            record = DataRecord(
                record_id=user_id,
                record_type="user",
                data=user_data
            )
            
            await self._store_record("users", record)
            self.logger.info(f"User data stored: {user_id}")
            return user_id
            
        except Exception as e:
            self.logger.error(f"Failed to store user data: {e}")
            raise
    
    async def get_user_data(self, user_id: UUID) -> Optional[Dict[str, Any]]:
        """获取用户数据"""
        try:
            record = await self._get_record("users", user_id)
            if record:
                return record.data
            return None
            
        except Exception as e:
            self.logger.error(f"Failed to get user data {user_id}: {e}")
            return None
    
    async def query_records(
        self,
        record_type: str,
        filters: Optional[Dict[str, Any]] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        order_by: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """查询记录"""
        try:
            results = []
            
            if record_type not in self._memory_store:
                return results
            
            records = list(self._memory_store[record_type].values())
            
            # 应用过滤器
            if filters:
                filtered_records = []
                for record in records:
                    match = True
                    for key, value in filters.items():
                        if key in record.data and record.data[key] != value:
                            match = False
                            break
                    if match:
                        filtered_records.append(record)
                records = filtered_records
            
            # 排序
            if order_by:
                if order_by.startswith("-"):
                    # 倒序
                    field = order_by[1:]
                    records.sort(key=lambda x: getattr(x, field, x.created_at), reverse=True)
                else:
                    # 正序
                    records.sort(key=lambda x: getattr(x, order_by, x.created_at))
            else:
                # 默认按创建时间倒序
                records.sort(key=lambda x: x.created_at, reverse=True)
            
            # 分页
            if offset:
                records = records[offset:]
            if limit:
                records = records[:limit]
            
            results = [record.to_dict() for record in records]
            
            self.logger.info(f"Query {record_type} returned {len(results)} records")
            return results
            
        except Exception as e:
            self.logger.error(f"Failed to query records: {e}")
            return []
    
    async def delete_record(self, record_type: str, record_id: UUID) -> bool:
        """删除记录"""
        try:
            if record_type in self._memory_store and record_id in self._memory_store[record_type]:
                del self._memory_store[record_type][record_id]
                
                # 从缓存中删除
                cache_key = f"{record_type}:{record_id}"
                self._cache.pop(cache_key, None)
                
                self.logger.info(f"Record deleted: {record_type}:{record_id}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to delete record {record_type}:{record_id}: {e}")
            return False
    
    def cache_get(self, key: str) -> Optional[Any]:
        """从缓存获取数据"""
        try:
            cache_item = self._cache.get(key)
            if cache_item:
                if cache_item.is_expired():
                    del self._cache[key]
                    self._cache_misses += 1
                    return None
                
                self._cache_hits += 1
                return cache_item.access()
            
            self._cache_misses += 1
            return None
            
        except Exception as e:
            self.logger.error(f"Failed to get cache {key}: {e}")
            return None
    
    def cache_set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存数据"""
        try:
            # 检查缓存大小限制
            if len(self._cache) >= self._max_cache_size:
                self._evict_cache_items()
            
            ttl = ttl or self._default_cache_ttl
            self._cache[key] = CacheItem(key, value, ttl)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to set cache {key}: {e}")
            return False
    
    def cache_delete(self, key: str) -> bool:
        """删除缓存数据"""
        try:
            if key in self._cache:
                del self._cache[key]
                return True
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to delete cache {key}: {e}")
            return False
    
    async def batch_store_records(self, records: List[tuple[str, DataRecord]]) -> List[UUID]:
        """批量存储记录"""
        try:
            stored_ids = []
            
            for record_type, record in records:
                await self._store_record(record_type, record)
                stored_ids.append(record.record_id)
            
            self.logger.info(f"Batch stored {len(records)} records")
            return stored_ids
            
        except Exception as e:
            self.logger.error(f"Failed to batch store records: {e}")
            raise
    
    async def batch_get_records(self, requests: List[tuple[str, UUID]]) -> List[Optional[Dict[str, Any]]]:
        """批量获取记录"""
        try:
            results = []
            
            for record_type, record_id in requests:
                record = await self._get_record(record_type, record_id)
                results.append(record.data if record else None)
            
            return results
            
        except Exception as e:
            self.logger.error(f"Failed to batch get records: {e}")
            return [None] * len(requests)
    
    async def verify_data_integrity(self) -> Dict[str, Any]:
        """验证数据完整性"""
        try:
            integrity_report = {
                "status": "healthy",
                "issues": [],
                "statistics": {},
                "recommendations": []
            }
            
            # 检查内存存储一致性
            for record_type, records in self._memory_store.items():
                type_stats = {
                    "total_records": len(records),
                    "orphaned_records": 0,
                    "corrupted_records": 0
                }
                
                for record_id, record in records.items():
                    # 检查记录完整性
                    if not isinstance(record, DataRecord):
                        type_stats["corrupted_records"] += 1
                        integrity_report["issues"].append(f"Corrupted record in {record_type}: {record_id}")
                    
                    # 检查时间戳逻辑
                    if record.updated_at < record.created_at:
                        integrity_report["issues"].append(f"Invalid timestamp in {record_type}: {record_id}")
                
                integrity_report["statistics"][record_type] = type_stats
            
            # 检查缓存一致性
            cache_issues = 0
            for cache_key, cache_item in self._cache.items():
                if cache_item.is_expired():
                    cache_issues += 1
            
            if cache_issues > 0:
                integrity_report["issues"].append(f"Found {cache_issues} expired cache items")
                integrity_report["recommendations"].append("Run cache cleanup")
            
            # 设置整体状态
            if len(integrity_report["issues"]) > 0:
                integrity_report["status"] = "issues_found"
            
            return integrity_report
            
        except Exception as e:
            self.logger.error(f"Failed to verify data integrity: {e}")
            return {
                "status": "error",
                "error": str(e)
            }
    
    async def cleanup_orphaned_data(self) -> Dict[str, int]:
        """清理孤立数据"""
        try:
            cleanup_stats = {
                "expired_cache_items": 0,
                "old_records": 0
            }
            
            # 清理过期缓存项
            expired_keys = []
            for key, cache_item in self._cache.items():
                if cache_item.is_expired():
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self._cache[key]
                cleanup_stats["expired_cache_items"] += 1
            
            # 清理老旧记录（超过30天的记录）
            cutoff_date = datetime.now() - timedelta(days=30)
            for record_type, records in self._memory_store.items():
                old_record_ids = []
                for record_id, record in records.items():
                    if record.created_at < cutoff_date:
                        old_record_ids.append(record_id)
                
                for record_id in old_record_ids:
                    del records[record_id]
                    cleanup_stats["old_records"] += 1
            
            self.logger.info(f"Cleanup completed: {cleanup_stats}")
            return cleanup_stats
            
        except Exception as e:
            self.logger.error(f"Failed to cleanup orphaned data: {e}")
            return {}
    
    async def _store_record(self, record_type: str, record: DataRecord) -> None:
        """存储记录（内部方法）"""
        try:
            # 存储到内存
            if record_type not in self._memory_store:
                self._memory_store[record_type] = {}
            
            self._memory_store[record_type][record.record_id] = record
            
            # 更新缓存
            cache_key = f"{record_type}:{record.record_id}"
            self.cache_set(cache_key, record.data)
            
            # 如果有数据库连接，也存储到数据库
            if self._db_connection and self._db_type == DatabaseType.SQLITE:
                await self._store_to_sqlite(record_type, record)
            
            self._total_writes += 1
            
        except Exception as e:
            self.logger.error(f"Failed to store record: {e}")
            raise
    
    async def _get_record(self, record_type: str, record_id: UUID) -> Optional[DataRecord]:
        """获取记录（内部方法）"""
        try:
            # 先尝试从缓存获取
            cache_key = f"{record_type}:{record_id}"
            cached_data = self.cache_get(cache_key)
            if cached_data:
                return DataRecord(
                    record_id=record_id,
                    record_type=record_type,
                    data=cached_data
                )
            
            # 从内存存储获取
            if record_type in self._memory_store and record_id in self._memory_store[record_type]:
                record = self._memory_store[record_type][record_id]
                
                # 更新缓存
                self.cache_set(cache_key, record.data)
                
                self._total_reads += 1
                return record
            
            # 如果有数据库连接，从数据库获取
            if self._db_connection and self._db_type == DatabaseType.SQLITE:
                record = await self._get_from_sqlite(record_type, record_id)
                if record:
                    # 存储到内存缓存
                    self._memory_store[record_type][record_id] = record
                    self.cache_set(cache_key, record.data)
                    self._total_reads += 1
                    return record
            
            return None
            
        except Exception as e:
            self.logger.error(f"Failed to get record: {e}")
            return None
    
    async def _initialize_sqlite(self) -> None:
        """初始化SQLite数据库"""
        try:
            self._db_type = DatabaseType.SQLITE
            self._db_connection = await aiosqlite.connect(self._db_path)
            
            self.logger.info("SQLite database initialized")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize SQLite: {e}")
            raise
    
    async def _initialize_postgresql(self) -> None:
        """初始化PostgreSQL数据库"""
        try:
            # PostgreSQL 初始化代码
            # 这里应该使用 asyncpg 或其他异步PostgreSQL驱动
            self._db_type = DatabaseType.POSTGRESQL
            
            self.logger.info("PostgreSQL database initialized")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize PostgreSQL: {e}")
            # 回退到SQLite
            await self._initialize_sqlite()
    
    async def _create_tables(self) -> None:
        """创建数据库表"""
        try:
            if self._db_type == DatabaseType.SQLITE and self._db_connection:
                await self._create_sqlite_tables()
            elif self._db_type == DatabaseType.POSTGRESQL and self._db_connection:
                await self._create_postgresql_tables()
            
            self.logger.info("Database tables created")
            
        except Exception as e:
            self.logger.error(f"Failed to create tables: {e}")
    
    async def _create_sqlite_tables(self) -> None:
        """创建SQLite表"""
        try:
            # 通用记录表
            await self._db_connection.execute("""
                CREATE TABLE IF NOT EXISTS records (
                    record_id TEXT PRIMARY KEY,
                    record_type TEXT NOT NULL,
                    data TEXT NOT NULL,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL
                )
            """)
            
            # 创建索引
            await self._db_connection.execute("""
                CREATE INDEX IF NOT EXISTS idx_records_type 
                ON records(record_type)
            """)
            
            await self._db_connection.execute("""
                CREATE INDEX IF NOT EXISTS idx_records_created_at 
                ON records(created_at)
            """)
            
            await self._db_connection.commit()
            
        except Exception as e:
            self.logger.error(f"Failed to create SQLite tables: {e}")
            raise
    
    async def _create_postgresql_tables(self) -> None:
        """创建PostgreSQL表"""
        # PostgreSQL 表创建代码
        pass
    
    async def _store_to_sqlite(self, record_type: str, record: DataRecord) -> None:
        """存储到SQLite数据库"""
        try:
            await self._db_connection.execute("""
                INSERT OR REPLACE INTO records 
                (record_id, record_type, data, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?)
            """, (
                str(record.record_id),
                record.record_type,
                json.dumps(record.data, default=str),
                record.created_at.isoformat(),
                record.updated_at.isoformat()
            ))
            
            await self._db_connection.commit()
            
        except Exception as e:
            self.logger.error(f"Failed to store to SQLite: {e}")
            raise
    
    async def _get_from_sqlite(self, record_type: str, record_id: UUID) -> Optional[DataRecord]:
        """从SQLite数据库获取"""
        try:
            cursor = await self._db_connection.execute("""
                SELECT record_id, record_type, data, created_at, updated_at
                FROM records
                WHERE record_id = ? AND record_type = ?
            """, (str(record_id), record_type))
            
            row = await cursor.fetchone()
            if row:
                return DataRecord(
                    record_id=UUID(row[0]),
                    record_type=row[1],
                    data=json.loads(row[2]),
                    created_at=datetime.fromisoformat(row[3]),
                    updated_at=datetime.fromisoformat(row[4])
                )
            
            return None
            
        except Exception as e:
            self.logger.error(f"Failed to get from SQLite: {e}")
            return None
    
    def _evict_cache_items(self) -> None:
        """清理缓存项"""
        try:
            if self._cache_strategy == CacheStrategy.LRU:
                # LRU: 删除最近最少使用的
                items_to_remove = sorted(
                    self._cache.items(),
                    key=lambda x: x[1].last_accessed
                )[:len(self._cache) // 4]  # 删除25%
                
            elif self._cache_strategy == CacheStrategy.LFU:
                # LFU: 删除使用频率最低的
                items_to_remove = sorted(
                    self._cache.items(),
                    key=lambda x: x[1].access_count
                )[:len(self._cache) // 4]
                
            else:
                # 默认删除最旧的
                items_to_remove = sorted(
                    self._cache.items(),
                    key=lambda x: x[1].created_at
                )[:len(self._cache) // 4]
            
            for key, _ in items_to_remove:
                del self._cache[key]
                
            self.logger.info(f"Evicted {len(items_to_remove)} cache items")
            
        except Exception as e:
            self.logger.error(f"Failed to evict cache items: {e}")
    
    async def _cache_cleanup_task(self) -> None:
        """缓存清理定时任务"""
        while True:
            try:
                await asyncio.sleep(300)  # 每5分钟清理一次
                
                expired_keys = []
                for key, cache_item in self._cache.items():
                    if cache_item.is_expired():
                        expired_keys.append(key)
                
                for key in expired_keys:
                    del self._cache[key]
                
                if expired_keys:
                    self.logger.info(f"Cleaned up {len(expired_keys)} expired cache items")
                    
            except Exception as e:
                self.logger.error(f"Error in cache cleanup task: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        try:
            total_cache_requests = self._cache_hits + self._cache_misses
            cache_hit_rate = (self._cache_hits / total_cache_requests * 100) if total_cache_requests > 0 else 0
            
            return {
                "database_type": self._db_type.value,
                "initialized": self._initialized,
                "total_reads": self._total_reads,
                "total_writes": self._total_writes,
                "cache_size": len(self._cache),
                "cache_hits": self._cache_hits,
                "cache_misses": self._cache_misses,
                "cache_hit_rate": round(cache_hit_rate, 2),
                "memory_store_size": {
                    record_type: len(records) 
                    for record_type, records in self._memory_store.items()
                },
                "total_memory_records": sum(len(records) for records in self._memory_store.values())
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get statistics: {e}")
            return {}


# 全局数据库管理器实例
database_manager: Optional[DatabaseManager] = None


async def get_database_manager() -> DatabaseManager:
    """获取数据库管理器实例"""
    global database_manager
    if database_manager is None:
        database_manager = DatabaseManager()
        await database_manager.initialize()
    return database_manager


async def close_database_manager() -> None:
    """关闭数据库管理器"""
    global database_manager
    if database_manager:
        await database_manager.cleanup()
        database_manager = None 