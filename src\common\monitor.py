"""
系统监控模块

负责系统指标收集、健康检查、性能监控和告警管理
"""

import asyncio
import psutil
import json
import time
from typing import Dict, List, Optional, Any, Callable
from uuid import UUID, uuid4
from datetime import datetime, timedelta
from enum import Enum
from dataclasses import dataclass, asdict

from src.common.logger import get_logger


class AlertLevel(str, Enum):
    """告警级别"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class HealthStatus(str, Enum):
    """健康状态"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"


@dataclass
class SystemMetrics:
    """系统指标"""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    memory_available_mb: float
    disk_percent: float
    disk_used_gb: float
    disk_free_gb: float
    network_bytes_sent: int
    network_bytes_recv: int
    process_count: int
    load_average: List[float]


@dataclass
class Alert:
    """告警信息"""
    alert_id: UUID
    level: AlertLevel
    title: str
    message: str
    source: str
    metric_name: Optional[str]
    metric_value: Optional[float]
    threshold: Optional[float]
    timestamp: datetime
    resolved: bool = False
    resolved_at: Optional[datetime] = None


@dataclass
class HealthCheck:
    """健康检查"""
    name: str
    status: HealthStatus
    message: str
    details: Dict[str, Any]
    last_check: datetime
    response_time_ms: float


class SystemMonitor:
    """
    系统监控器
    
    负责系统指标收集、健康检查、性能监控和告警管理
    """
    
    def __init__(self, collection_interval: int = 30):
        self.logger = get_logger(__name__)
        self._initialized = False
        self._collection_interval = collection_interval
        
        # 监控任务
        self._monitor_task: Optional[asyncio.Task] = None
        self._health_check_task: Optional[asyncio.Task] = None
        
        # 数据存储
        self._metrics_history: List[SystemMetrics] = []
        self._max_history_size = 1000  # 保留最近1000条记录
        
        # 告警管理
        self._alerts: Dict[UUID, Alert] = {}
        self._active_alerts: Dict[str, Alert] = {}  # source -> alert
        self._alert_thresholds = self._init_default_thresholds()
        
        # 健康检查
        self._health_checks: Dict[str, Callable] = {}
        self._health_status: Dict[str, HealthCheck] = {}
        
        # 统计信息
        self._total_alerts = 0
        self._resolved_alerts = 0
        self._metrics_collected = 0
        
    async def initialize(self) -> None:
        """初始化监控器"""
        try:
            self.logger.info("Initializing SystemMonitor")
            
            # 注册默认健康检查
            await self._register_default_health_checks()
            
            # 启动监控任务
            self._monitor_task = asyncio.create_task(self._monitor_loop())
            self._health_check_task = asyncio.create_task(self._health_check_loop())
            
            self._initialized = True
            self.logger.info(f"SystemMonitor initialized, collection interval: {self._collection_interval}s")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize SystemMonitor: {e}")
            raise
    
    async def cleanup(self) -> None:
        """清理资源"""
        try:
            self.logger.info("Cleaning up SystemMonitor")
            
            # 停止监控任务
            if self._monitor_task:
                self._monitor_task.cancel()
                try:
                    await self._monitor_task
                except asyncio.CancelledError:
                    pass
            
            if self._health_check_task:
                self._health_check_task.cancel()
                try:
                    await self._health_check_task
                except asyncio.CancelledError:
                    pass
            
            # 清理数据
            self._metrics_history.clear()
            self._alerts.clear()
            self._active_alerts.clear()
            self._health_status.clear()
            
            self._initialized = False
            self.logger.info("SystemMonitor cleaned up successfully")
            
        except Exception as e:
            self.logger.error(f"Error during SystemMonitor cleanup: {e}")
    
    async def get_current_metrics(self) -> SystemMetrics:
        """获取当前系统指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存信息
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_used_mb = memory.used / 1024 / 1024
            memory_available_mb = memory.available / 1024 / 1024
            
            # 磁盘信息
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent
            disk_used_gb = disk.used / 1024 / 1024 / 1024
            disk_free_gb = disk.free / 1024 / 1024 / 1024
            
            # 网络信息
            network = psutil.net_io_counters()
            network_bytes_sent = network.bytes_sent
            network_bytes_recv = network.bytes_recv
            
            # 进程数量
            process_count = len(psutil.pids())
            
            # 负载平均值（Linux/Mac）
            try:
                load_average = list(psutil.getloadavg())
            except AttributeError:
                # Windows不支持getloadavg
                load_average = [0.0, 0.0, 0.0]
            
            metrics = SystemMetrics(
                timestamp=datetime.now(),
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                memory_used_mb=memory_used_mb,
                memory_available_mb=memory_available_mb,
                disk_percent=disk_percent,
                disk_used_gb=disk_used_gb,
                disk_free_gb=disk_free_gb,
                network_bytes_sent=network_bytes_sent,
                network_bytes_recv=network_bytes_recv,
                process_count=process_count,
                load_average=load_average
            )
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"Failed to collect system metrics: {e}")
            raise
    
    async def get_metrics_history(
        self,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        limit: int = 100
    ) -> List[SystemMetrics]:
        """获取历史指标"""
        try:
            metrics = self._metrics_history.copy()
            
            # 时间过滤
            if start_time:
                metrics = [m for m in metrics if m.timestamp >= start_time]
            if end_time:
                metrics = [m for m in metrics if m.timestamp <= end_time]
            
            # 限制数量
            metrics = metrics[-limit:]
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"Failed to get metrics history: {e}")
            return []
    
    async def register_health_check(self, name: str, check_func: Callable) -> None:
        """注册健康检查"""
        try:
            self._health_checks[name] = check_func
            self.logger.info(f"Health check registered: {name}")
            
        except Exception as e:
            self.logger.error(f"Failed to register health check {name}: {e}")
    
    async def get_health_status(self, check_name: Optional[str] = None) -> Dict[str, Any]:
        """获取健康状态"""
        try:
            if check_name:
                # 获取特定检查的状态
                health_check = self._health_status.get(check_name)
                if health_check:
                    return asdict(health_check)
                else:
                    return {"error": f"Health check '{check_name}' not found"}
            else:
                # 获取所有健康检查状态
                overall_status = HealthStatus.HEALTHY
                health_checks = {}
                
                for name, health_check in self._health_status.items():
                    health_checks[name] = asdict(health_check)
                    
                    # 确定整体状态
                    if health_check.status == HealthStatus.UNHEALTHY:
                        overall_status = HealthStatus.UNHEALTHY
                    elif health_check.status == HealthStatus.DEGRADED and overall_status == HealthStatus.HEALTHY:
                        overall_status = HealthStatus.DEGRADED
                
                return {
                    "overall_status": overall_status.value,
                    "checks": health_checks,
                    "last_updated": datetime.now().isoformat()
                }
                
        except Exception as e:
            self.logger.error(f"Failed to get health status: {e}")
            return {"error": str(e)}
    
    async def get_alerts(
        self,
        level: Optional[AlertLevel] = None,
        active_only: bool = False,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """获取告警信息"""
        try:
            alerts = list(self._alerts.values())
            
            # 过滤
            if level:
                alerts = [a for a in alerts if a.level == level]
            if active_only:
                alerts = [a for a in alerts if not a.resolved]
            
            # 排序并限制数量
            alerts.sort(key=lambda x: x.timestamp, reverse=True)
            alerts = alerts[:limit]
            
            return [asdict(alert) for alert in alerts]
            
        except Exception as e:
            self.logger.error(f"Failed to get alerts: {e}")
            return []
    
    async def resolve_alert(self, alert_id: UUID) -> bool:
        """解决告警"""
        try:
            alert = self._alerts.get(alert_id)
            if not alert:
                return False
            
            alert.resolved = True
            alert.resolved_at = datetime.now()
            
            # 从活跃告警中移除
            if alert.source in self._active_alerts:
                del self._active_alerts[alert.source]
            
            self._resolved_alerts += 1
            self.logger.info(f"Alert resolved: {alert.title}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to resolve alert {alert_id}: {e}")
            return False
    
    async def _monitor_loop(self) -> None:
        """监控循环"""
        while self._initialized:
            try:
                # 收集指标
                metrics = await self.get_current_metrics()
                
                # 存储指标
                self._metrics_history.append(metrics)
                self._metrics_collected += 1
                
                # 限制历史记录大小
                if len(self._metrics_history) > self._max_history_size:
                    self._metrics_history = self._metrics_history[-self._max_history_size:]
                
                # 检查告警条件
                await self._check_alert_conditions(metrics)
                
                # 等待下次收集
                await asyncio.sleep(self._collection_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in monitor loop: {e}")
                await asyncio.sleep(self._collection_interval)
    
    async def _health_check_loop(self) -> None:
        """健康检查循环"""
        while self._initialized:
            try:
                # 执行所有健康检查
                for name, check_func in self._health_checks.items():
                    await self._run_health_check(name, check_func)
                
                # 每分钟检查一次
                await asyncio.sleep(60)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in health check loop: {e}")
                await asyncio.sleep(60)
    
    async def _run_health_check(self, name: str, check_func: Callable) -> None:
        """运行单个健康检查"""
        try:
            start_time = time.time()
            
            # 执行检查函数
            if asyncio.iscoroutinefunction(check_func):
                result = await check_func()
            else:
                result = check_func()
            
            response_time_ms = (time.time() - start_time) * 1000
            
            # 解析结果
            if isinstance(result, dict):
                status = HealthStatus(result.get("status", HealthStatus.UNKNOWN))
                message = result.get("message", "")
                details = result.get("details", {})
            elif isinstance(result, bool):
                status = HealthStatus.HEALTHY if result else HealthStatus.UNHEALTHY
                message = "Check passed" if result else "Check failed"
                details = {}
            else:
                status = HealthStatus.UNKNOWN
                message = str(result)
                details = {}
            
            # 创建健康检查结果
            health_check = HealthCheck(
                name=name,
                status=status,
                message=message,
                details=details,
                last_check=datetime.now(),
                response_time_ms=response_time_ms
            )
            
            self._health_status[name] = health_check
            
            # 如果检查失败，生成告警
            if status == HealthStatus.UNHEALTHY:
                await self._create_alert(
                    level=AlertLevel.ERROR,
                    title=f"Health check failed: {name}",
                    message=message,
                    source=f"health_check_{name}"
                )
            
        except Exception as e:
            self.logger.error(f"Health check '{name}' failed: {e}")
            
            # 创建错误健康检查结果
            health_check = HealthCheck(
                name=name,
                status=HealthStatus.UNHEALTHY,
                message=f"Check execution failed: {str(e)}",
                details={"error": str(e)},
                last_check=datetime.now(),
                response_time_ms=0
            )
            
            self._health_status[name] = health_check
    
    async def _check_alert_conditions(self, metrics: SystemMetrics) -> None:
        """检查告警条件"""
        try:
            # 检查CPU使用率
            if metrics.cpu_percent > self._alert_thresholds["cpu_percent"]:
                await self._create_alert(
                    level=AlertLevel.WARNING,
                    title="High CPU Usage",
                    message=f"CPU usage is {metrics.cpu_percent:.1f}%",
                    source="cpu_usage",
                    metric_name="cpu_percent",
                    metric_value=metrics.cpu_percent,
                    threshold=self._alert_thresholds["cpu_percent"]
                )
            
            # 检查内存使用率
            if metrics.memory_percent > self._alert_thresholds["memory_percent"]:
                await self._create_alert(
                    level=AlertLevel.WARNING,
                    title="High Memory Usage",
                    message=f"Memory usage is {metrics.memory_percent:.1f}%",
                    source="memory_usage",
                    metric_name="memory_percent",
                    metric_value=metrics.memory_percent,
                    threshold=self._alert_thresholds["memory_percent"]
                )
            
            # 检查磁盘使用率
            if metrics.disk_percent > self._alert_thresholds["disk_percent"]:
                await self._create_alert(
                    level=AlertLevel.ERROR,
                    title="High Disk Usage",
                    message=f"Disk usage is {metrics.disk_percent:.1f}%",
                    source="disk_usage",
                    metric_name="disk_percent",
                    metric_value=metrics.disk_percent,
                    threshold=self._alert_thresholds["disk_percent"]
                )
            
        except Exception as e:
            self.logger.error(f"Error checking alert conditions: {e}")
    
    async def _create_alert(
        self,
        level: AlertLevel,
        title: str,
        message: str,
        source: str,
        metric_name: Optional[str] = None,
        metric_value: Optional[float] = None,
        threshold: Optional[float] = None
    ) -> None:
        """创建告警"""
        try:
            # 检查是否已有同源的活跃告警
            if source in self._active_alerts:
                existing_alert = self._active_alerts[source]
                if not existing_alert.resolved:
                    # 更新现有告警
                    existing_alert.message = message
                    existing_alert.timestamp = datetime.now()
                    if metric_value is not None:
                        existing_alert.metric_value = metric_value
                    return
            
            # 创建新告警
            alert_id = uuid4()
            alert = Alert(
                alert_id=alert_id,
                level=level,
                title=title,
                message=message,
                source=source,
                metric_name=metric_name,
                metric_value=metric_value,
                threshold=threshold,
                timestamp=datetime.now()
            )
            
            # 存储告警
            self._alerts[alert_id] = alert
            self._active_alerts[source] = alert
            self._total_alerts += 1
            
            self.logger.warning(f"Alert created: {title} - {message}")
            
        except Exception as e:
            self.logger.error(f"Failed to create alert: {e}")
    
    def _init_default_thresholds(self) -> Dict[str, float]:
        """初始化默认告警阈值"""
        return {
            "cpu_percent": 80.0,
            "memory_percent": 85.0,
            "disk_percent": 90.0,
            "response_time_ms": 5000.0
        }
    
    async def _register_default_health_checks(self) -> None:
        """注册默认健康检查"""
        try:
            # 系统资源检查
            await self.register_health_check("system_resources", self._check_system_resources)
            
            # 磁盘空间检查
            await self.register_health_check("disk_space", self._check_disk_space)
            
            # 进程数量检查
            await self.register_health_check("process_count", self._check_process_count)
            
        except Exception as e:
            self.logger.error(f"Failed to register default health checks: {e}")
    
    async def _check_system_resources(self) -> Dict[str, Any]:
        """检查系统资源"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory_percent = psutil.virtual_memory().percent
            
            if cpu_percent > 90 or memory_percent > 95:
                return {
                    "status": HealthStatus.UNHEALTHY,
                    "message": f"High resource usage: CPU {cpu_percent:.1f}%, Memory {memory_percent:.1f}%",
                    "details": {"cpu_percent": cpu_percent, "memory_percent": memory_percent}
                }
            elif cpu_percent > 70 or memory_percent > 80:
                return {
                    "status": HealthStatus.DEGRADED,
                    "message": f"Moderate resource usage: CPU {cpu_percent:.1f}%, Memory {memory_percent:.1f}%",
                    "details": {"cpu_percent": cpu_percent, "memory_percent": memory_percent}
                }
            else:
                return {
                    "status": HealthStatus.HEALTHY,
                    "message": f"Resources normal: CPU {cpu_percent:.1f}%, Memory {memory_percent:.1f}%",
                    "details": {"cpu_percent": cpu_percent, "memory_percent": memory_percent}
                }
                
        except Exception as e:
            return {
                "status": HealthStatus.UNHEALTHY,
                "message": f"Failed to check system resources: {str(e)}",
                "details": {"error": str(e)}
            }
    
    async def _check_disk_space(self) -> Dict[str, Any]:
        """检查磁盘空间"""
        try:
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent
            
            if disk_percent > 95:
                return {
                    "status": HealthStatus.UNHEALTHY,
                    "message": f"Critical disk usage: {disk_percent:.1f}%",
                    "details": {"disk_percent": disk_percent}
                }
            elif disk_percent > 85:
                return {
                    "status": HealthStatus.DEGRADED,
                    "message": f"High disk usage: {disk_percent:.1f}%",
                    "details": {"disk_percent": disk_percent}
                }
            else:
                return {
                    "status": HealthStatus.HEALTHY,
                    "message": f"Disk usage normal: {disk_percent:.1f}%",
                    "details": {"disk_percent": disk_percent}
                }
                
        except Exception as e:
            return {
                "status": HealthStatus.UNHEALTHY,
                "message": f"Failed to check disk space: {str(e)}",
                "details": {"error": str(e)}
            }
    
    async def _check_process_count(self) -> Dict[str, Any]:
        """检查进程数量"""
        try:
            process_count = len(psutil.pids())
            
            if process_count > 1000:
                return {
                    "status": HealthStatus.DEGRADED,
                    "message": f"High process count: {process_count}",
                    "details": {"process_count": process_count}
                }
            else:
                return {
                    "status": HealthStatus.HEALTHY,
                    "message": f"Process count normal: {process_count}",
                    "details": {"process_count": process_count}
                }
                
        except Exception as e:
            return {
                "status": HealthStatus.UNHEALTHY,
                "message": f"Failed to check process count: {str(e)}",
                "details": {"error": str(e)}
            }
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            "metrics_collected": self._metrics_collected,
            "history_size": len(self._metrics_history),
            "total_alerts": self._total_alerts,
            "active_alerts": len(self._active_alerts),
            "resolved_alerts": self._resolved_alerts,
            "health_checks": len(self._health_checks),
            "collection_interval": self._collection_interval,
            "initialized": self._initialized
        } 