"""
反馈处理器

负责收集、处理和应用测试执行反馈，实现智能优化循环
"""

import asyncio
import json
from typing import Dict, List, Optional, Any, Union
from uuid import UUID, uuid4
from datetime import datetime
from enum import Enum
from pathlib import Path

from src.common.logger import get_logger


class FeedbackType(str, Enum):
    """反馈类型"""
    SCRIPT_IMPROVEMENT = "script_improvement"
    TEST_CASE_QUALITY = "test_case_quality"
    EXECUTION_ISSUE = "execution_issue"
    PERFORMANCE_ISSUE = "performance_issue"
    SECURITY_CONCERN = "security_concern"
    USER_EXPERIENCE = "user_experience"
    GENERAL_SUGGESTION = "general_suggestion"


class FeedbackPriority(str, Enum):
    """反馈优先级"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class FeedbackItem:
    """反馈项"""
    
    def __init__(
        self,
        feedback_id: UUID,
        feedback_type: FeedbackType,
        content: str,
        source: str,
        priority: FeedbackPriority = FeedbackPriority.MEDIUM,
        metadata: Optional[Dict[str, Any]] = None,
        session_id: Optional[UUID] = None,
        test_case_id: Optional[UUID] = None,
        script_id: Optional[UUID] = None,
        execution_id: Optional[UUID] = None
    ):
        self.feedback_id = feedback_id
        self.feedback_type = feedback_type
        self.content = content
        self.source = source
        self.priority = priority
        self.metadata = metadata or {}
        self.session_id = session_id
        self.test_case_id = test_case_id
        self.script_id = script_id
        self.execution_id = execution_id
        self.created_at = datetime.now()
        self.processed = False
        self.impact_score = 0.0
        self.applied = False


class KnowledgeItem:
    """知识库项"""
    
    def __init__(
        self,
        knowledge_id: UUID,
        title: str,
        content: str,
        category: str,
        tags: List[str],
        source_feedback_ids: List[UUID],
        confidence: float = 0.5,
        usage_count: int = 0,
        metadata: Optional[Dict[str, Any]] = None
    ):
        self.knowledge_id = knowledge_id
        self.title = title
        self.content = content
        self.category = category
        self.tags = tags
        self.source_feedback_ids = source_feedback_ids
        self.confidence = confidence
        self.usage_count = usage_count
        self.metadata = metadata or {}
        self.created_at = datetime.now()
        self.updated_at = datetime.now()


class FeedbackProcessor:
    """
    反馈处理器
    
    负责收集、处理和应用测试执行反馈，实现智能优化循环
    """
    
    def __init__(self, ai_client: Optional[Any] = None):
        self.logger = get_logger(__name__)
        self._initialized = False
        self._ai_client = ai_client
        
        # 反馈存储
        self._feedback_items: Dict[UUID, FeedbackItem] = {}
        self._knowledge_base: Dict[UUID, KnowledgeItem] = {}
        
        # 处理队列
        self._feedback_queue: asyncio.Queue = asyncio.Queue()
        self._processing_task: Optional[asyncio.Task] = None
        
        # 统计信息
        self._total_feedback = 0
        self._processed_feedback = 0
        self._applied_feedback = 0
        self._feedback_by_type: Dict[str, int] = {}
        
    async def initialize(self) -> None:
        """初始化反馈处理器"""
        try:
            self.logger.info("Initializing FeedbackProcessor")
            
            # 初始化AI客户端
            if self._ai_client is None:
                try:
                    from src.ai.llm_client import LLMClient
                    self._ai_client = LLMClient()
                    await self._ai_client.initialize()
                except Exception as e:
                    self.logger.warning(f"AI client initialization failed, using fallback: {e}")
                    self._ai_client = None
            
            # 启动处理队列
            self._processing_task = asyncio.create_task(self._process_feedback_queue())
            
            # 加载现有知识库
            await self._load_knowledge_base()
            
            self._initialized = True
            self.logger.info("FeedbackProcessor initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize FeedbackProcessor: {e}")
            raise
    
    async def cleanup(self) -> None:
        """清理资源"""
        try:
            self.logger.info("Cleaning up FeedbackProcessor")
            
            # 停止处理任务
            if self._processing_task:
                self._processing_task.cancel()
                try:
                    await self._processing_task
                except asyncio.CancelledError:
                    pass
            
            # 保存知识库
            await self._save_knowledge_base()
            
            # 清理缓存
            self._feedback_items.clear()
            self._knowledge_base.clear()
            
            # 清理AI客户端
            if self._ai_client and hasattr(self._ai_client, 'cleanup'):
                await self._ai_client.cleanup()
            
            self._initialized = False
            self.logger.info("FeedbackProcessor cleaned up successfully")
            
        except Exception as e:
            self.logger.error(f"Error during FeedbackProcessor cleanup: {e}")
    
    async def submit_feedback(
        self,
        feedback_type: FeedbackType,
        content: str,
        source: str,
        priority: FeedbackPriority = FeedbackPriority.MEDIUM,
        metadata: Optional[Dict[str, Any]] = None,
        session_id: Optional[UUID] = None,
        test_case_id: Optional[UUID] = None,
        script_id: Optional[UUID] = None,
        execution_id: Optional[UUID] = None
    ) -> UUID:
        """
        提交反馈
        
        Args:
            feedback_type: 反馈类型
            content: 反馈内容
            source: 反馈来源
            priority: 优先级
            metadata: 元数据
            session_id: 会话ID
            test_case_id: 测试用例ID
            script_id: 脚本ID
            execution_id: 执行ID
            
        Returns:
            UUID: 反馈ID
        """
        if not self._initialized:
            raise RuntimeError("FeedbackProcessor not initialized")
        
        try:
            feedback_id = uuid4()
            
            feedback_item = FeedbackItem(
                feedback_id=feedback_id,
                feedback_type=feedback_type,
                content=content,
                source=source,
                priority=priority,
                metadata=metadata,
                session_id=session_id,
                test_case_id=test_case_id,
                script_id=script_id,
                execution_id=execution_id
            )
            
            # 存储反馈
            self._feedback_items[feedback_id] = feedback_item
            
            # 添加到处理队列
            await self._feedback_queue.put(feedback_item)
            
            # 更新统计
            self._total_feedback += 1
            self._feedback_by_type[feedback_type.value] = (
                self._feedback_by_type.get(feedback_type.value, 0) + 1
            )
            
            self.logger.info(f"Feedback submitted: {feedback_id}, type: {feedback_type}")
            return feedback_id
            
        except Exception as e:
            self.logger.error(f"Failed to submit feedback: {e}")
            raise
    
    async def get_feedback(self, feedback_id: UUID) -> Optional[FeedbackItem]:
        """获取反馈详情"""
        return self._feedback_items.get(feedback_id)
    
    async def list_feedback(
        self,
        feedback_type: Optional[FeedbackType] = None,
        processed: Optional[bool] = None,
        limit: int = 100
    ) -> List[FeedbackItem]:
        """获取反馈列表"""
        feedback_list = list(self._feedback_items.values())
        
        # 过滤
        if feedback_type:
            feedback_list = [f for f in feedback_list if f.feedback_type == feedback_type]
        if processed is not None:
            feedback_list = [f for f in feedback_list if f.processed == processed]
        
        # 排序并限制数量
        feedback_list.sort(key=lambda x: x.created_at, reverse=True)
        return feedback_list[:limit]
    
    async def search_knowledge(
        self,
        query: str,
        category: Optional[str] = None,
        tags: Optional[List[str]] = None,
        limit: int = 10
    ) -> List[KnowledgeItem]:
        """搜索知识库"""
        try:
            knowledge_list = list(self._knowledge_base.values())
            
            # 过滤
            if category:
                knowledge_list = [k for k in knowledge_list if k.category == category]
            if tags:
                knowledge_list = [k for k in knowledge_list if any(tag in k.tags for tag in tags)]
            
            # 简单的文本匹配搜索
            query_lower = query.lower()
            scored_items = []
            
            for item in knowledge_list:
                score = 0
                if query_lower in item.title.lower():
                    score += 10
                if query_lower in item.content.lower():
                    score += 5
                if any(query_lower in tag.lower() for tag in item.tags):
                    score += 3
                
                if score > 0:
                    scored_items.append((score, item))
            
            # 排序并返回
            scored_items.sort(key=lambda x: x[0], reverse=True)
            return [item for _, item in scored_items[:limit]]
            
        except Exception as e:
            self.logger.error(f"Failed to search knowledge: {e}")
            return []
    
    async def _process_feedback_queue(self) -> None:
        """处理反馈队列"""
        while True:
            try:
                # 从队列获取反馈
                feedback_item = await asyncio.wait_for(
                    self._feedback_queue.get(), timeout=1.0
                )
                
                # 处理反馈
                await self._process_feedback_item(feedback_item)
                
            except asyncio.TimeoutError:
                # 超时是正常的，继续循环
                continue
            except asyncio.CancelledError:
                self.logger.info("Feedback processing task cancelled")
                break
            except Exception as e:
                self.logger.error(f"Error processing feedback queue: {e}")
                await asyncio.sleep(1)
    
    async def _process_feedback_item(self, feedback_item: FeedbackItem) -> None:
        """处理单个反馈"""
        try:
            self.logger.info(f"Processing feedback: {feedback_item.feedback_id}")
            
            # 计算影响分数
            impact_score = await self._calculate_impact_score(feedback_item)
            feedback_item.impact_score = impact_score
            
            # AI分析反馈
            if self._ai_client:
                try:
                    analysis = await self._analyze_feedback_with_ai(feedback_item)
                    feedback_item.metadata["ai_analysis"] = analysis
                except Exception as e:
                    self.logger.warning(f"AI analysis failed for feedback {feedback_item.feedback_id}: {e}")
            
            # 更新知识库
            await self._update_knowledge_base(feedback_item)
            
            # 标记为已处理
            feedback_item.processed = True
            self._processed_feedback += 1
            
            self.logger.info(f"Feedback processed: {feedback_item.feedback_id}, impact: {impact_score:.2f}")
            
        except Exception as e:
            self.logger.error(f"Failed to process feedback {feedback_item.feedback_id}: {e}")
    
    async def _calculate_impact_score(self, feedback_item: FeedbackItem) -> float:
        """计算反馈影响分数"""
        try:
            score = 0.0
            
            # 基于优先级
            priority_scores = {
                FeedbackPriority.LOW: 1.0,
                FeedbackPriority.MEDIUM: 2.0,
                FeedbackPriority.HIGH: 4.0,
                FeedbackPriority.CRITICAL: 8.0
            }
            score += priority_scores.get(feedback_item.priority, 1.0)
            
            # 基于反馈类型
            type_scores = {
                FeedbackType.SECURITY_CONCERN: 5.0,
                FeedbackType.PERFORMANCE_ISSUE: 3.0,
                FeedbackType.EXECUTION_ISSUE: 4.0,
                FeedbackType.SCRIPT_IMPROVEMENT: 2.0,
                FeedbackType.TEST_CASE_QUALITY: 2.0,
                FeedbackType.USER_EXPERIENCE: 1.5,
                FeedbackType.GENERAL_SUGGESTION: 1.0
            }
            score += type_scores.get(feedback_item.feedback_type, 1.0)
            
            # 基于内容长度（详细的反馈可能更有价值）
            content_length_bonus = min(len(feedback_item.content) / 100, 2.0)
            score += content_length_bonus
            
            # 基于关联的实体数量
            entity_count = sum([
                1 if feedback_item.session_id else 0,
                1 if feedback_item.test_case_id else 0,
                1 if feedback_item.script_id else 0,
                1 if feedback_item.execution_id else 0
            ])
            score += entity_count * 0.5
            
            return min(score, 10.0)  # 最大分数10
            
        except Exception as e:
            self.logger.error(f"Failed to calculate impact score: {e}")
            return 1.0
    
    async def _analyze_feedback_with_ai(self, feedback_item: FeedbackItem) -> Dict[str, Any]:
        """使用AI分析反馈"""
        try:
            prompt = f"""
            分析以下测试反馈，提供结构化的分析结果：

            反馈类型: {feedback_item.feedback_type}
            反馈内容: {feedback_item.content}
            来源: {feedback_item.source}
            优先级: {feedback_item.priority}

            请分析：
            1. 问题类型和严重程度
            2. 可能的根本原因
            3. 改进建议
            4. 影响范围
            5. 相关的最佳实践

            返回JSON格式的分析结果。
            """
            
            response = await self._ai_client.generate_response(prompt)
            
            # 尝试解析JSON响应
            try:
                analysis = json.loads(response)
            except json.JSONDecodeError:
                # 如果不是JSON，创建简单的分析结构
                analysis = {
                    "summary": response[:200],
                    "raw_response": response
                }
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"Failed to analyze feedback with AI: {e}")
            return {"error": str(e)}
    
    async def _update_knowledge_base(self, feedback_item: FeedbackItem) -> None:
        """更新知识库"""
        try:
            # 检查是否有相似的知识项
            similar_items = await self._find_similar_knowledge(feedback_item)
            
            if similar_items:
                # 更新现有知识项
                for item in similar_items:
                    item.source_feedback_ids.append(feedback_item.feedback_id)
                    item.usage_count += 1
                    item.updated_at = datetime.now()
                    
                    # 增加置信度
                    item.confidence = min(item.confidence + 0.1, 1.0)
            else:
                # 创建新的知识项
                await self._create_knowledge_item(feedback_item)
            
        except Exception as e:
            self.logger.error(f"Failed to update knowledge base: {e}")
    
    async def _find_similar_knowledge(self, feedback_item: FeedbackItem) -> List[KnowledgeItem]:
        """查找相似的知识项"""
        try:
            similar_items = []
            
            for item in self._knowledge_base.values():
                # 简单的相似度计算
                if item.category == feedback_item.feedback_type.value:
                    # 检查内容相似性（简单的关键词匹配）
                    feedback_words = set(feedback_item.content.lower().split())
                    knowledge_words = set(item.content.lower().split())
                    common_words = feedback_words.intersection(knowledge_words)
                    
                    if len(common_words) >= 3:  # 至少3个共同词汇
                        similar_items.append(item)
            
            return similar_items
            
        except Exception as e:
            self.logger.error(f"Failed to find similar knowledge: {e}")
            return []
    
    async def _create_knowledge_item(self, feedback_item: FeedbackItem) -> None:
        """创建新的知识项"""
        try:
            knowledge_id = uuid4()
            
            # 生成标题
            title = f"{feedback_item.feedback_type.value.replace('_', ' ').title()}"
            if feedback_item.metadata.get("ai_analysis", {}).get("summary"):
                title = feedback_item.metadata["ai_analysis"]["summary"][:50] + "..."
            
            # 生成标签
            tags = [feedback_item.feedback_type.value, feedback_item.source]
            if feedback_item.priority != FeedbackPriority.MEDIUM:
                tags.append(feedback_item.priority.value)
            
            # 创建知识项
            knowledge_item = KnowledgeItem(
                knowledge_id=knowledge_id,
                title=title,
                content=feedback_item.content,
                category=feedback_item.feedback_type.value,
                tags=tags,
                source_feedback_ids=[feedback_item.feedback_id],
                confidence=0.5,
                usage_count=1,
                metadata={
                    "created_from_feedback": str(feedback_item.feedback_id),
                    "ai_analysis": feedback_item.metadata.get("ai_analysis", {})
                }
            )
            
            self._knowledge_base[knowledge_id] = knowledge_item
            
            self.logger.info(f"Created knowledge item: {knowledge_id}")
            
        except Exception as e:
            self.logger.error(f"Failed to create knowledge item: {e}")
    
    async def _load_knowledge_base(self) -> None:
        """加载知识库"""
        try:
            # 这里可以从文件或数据库加载
            # 目前使用内存存储，重启后数据会丢失
            self.logger.info("Knowledge base loaded from memory")
            
        except Exception as e:
            self.logger.error(f"Failed to load knowledge base: {e}")
    
    async def _save_knowledge_base(self) -> None:
        """保存知识库"""
        try:
            # 这里可以保存到文件或数据库
            # 目前使用内存存储，重启后数据会丢失
            self.logger.info("Knowledge base saved to memory")
            
        except Exception as e:
            self.logger.error(f"Failed to save knowledge base: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            "total_feedback": self._total_feedback,
            "processed_feedback": self._processed_feedback,
            "applied_feedback": self._applied_feedback,
            "feedback_by_type": self._feedback_by_type,
            "knowledge_base_size": len(self._knowledge_base),
            "queue_size": self._feedback_queue.qsize(),
            "initialized": self._initialized
        } 