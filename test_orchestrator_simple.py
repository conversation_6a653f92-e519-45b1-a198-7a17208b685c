"""
简单的 OrchestratorService 测试
用于验证基本功能是否正常工作
"""

import asyncio
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.abspath('.'))

async def test_basic_functionality():
    """测试基本功能"""
    try:
        # 导入必要的模块
        from src.orchestrator.service import OrchestratorService
        from src.orchestrator.models import TestCaseGenerationRequest
        
        print("✅ 成功导入模块")
        
        # 创建服务实例
        orchestrator = OrchestratorService()
        print("✅ 成功创建 OrchestratorService 实例")
        
        # 初始化服务
        await orchestrator.initialize()
        print("✅ 成功初始化服务")
        
        # 创建会话
        session = await orchestrator.create_session()
        print(f"✅ 成功创建会话: {session.session_id}")
        
        # 测试会话检索
        retrieved_session = await orchestrator.get_session(session.session_id)
        assert retrieved_session is not None
        assert retrieved_session.session_id == session.session_id
        print("✅ 会话检索测试通过")
        
        # 测试测试用例生成
        request = TestCaseGenerationRequest(
            session_id=session.session_id,
            requirement_text="用户登录功能测试",
            context={"domain": "用户管理"},
            generation_options={"max_test_cases": 2}
        )
        
        response = await orchestrator.generate_test_cases(request)
        assert response.status.value == "completed"
        assert len(response.test_cases) > 0
        print(f"✅ 测试用例生成成功，生成了 {len(response.test_cases)} 个测试用例")
        
        # 清理
        await orchestrator.cleanup()
        print("✅ 服务清理完成")
        
        print("\n🎉 所有基本功能测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_error_handling():
    """测试错误处理"""
    try:
        from src.orchestrator.service import OrchestratorService
        from uuid import uuid4
        
        orchestrator = OrchestratorService()
        await orchestrator.initialize()
        
        # 测试获取不存在的会话
        non_existent_id = uuid4()
        result = await orchestrator.get_session(non_existent_id)
        assert result is None
        print("✅ 不存在会话的处理正确")
        
        await orchestrator.cleanup()
        return True
        
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("开始 OrchestratorService 基本功能测试...\n")
    
    # 运行基本功能测试
    basic_test_passed = await test_basic_functionality()
    
    print("\n" + "="*50)
    
    # 运行错误处理测试
    error_test_passed = await test_error_handling()
    
    print("\n" + "="*50)
    print("测试总结:")
    print(f"基本功能测试: {'✅ 通过' if basic_test_passed else '❌ 失败'}")
    print(f"错误处理测试: {'✅ 通过' if error_test_passed else '❌ 失败'}")
    
    if basic_test_passed and error_test_passed:
        print("\n🎉 所有测试通过！OrchestratorService 基本功能正常。")
        return 0
    else:
        print("\n❌ 部分测试失败，需要进一步检查。")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
