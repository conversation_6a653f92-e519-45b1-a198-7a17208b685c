"""
增强的配置模块 - 支持环境变量和安全配置
"""

from typing import List, Optional
from pydantic import Field
from pydantic_settings import BaseSettings


class SecuritySettings(BaseSettings):
    """安全配置"""
    cors_origins: List[str] = Field(
        default_factory=lambda: ["http://localhost:3000", "http://localhost:8080"],
        description="允许的CORS来源"
    )
    secret_key: str = Field(
        default="dev-secret-key-change-in-production",
        description="应用密钥"
    )
    api_key_header: str = Field(default="X-API-Key", description="API密钥头")
    rate_limit_per_minute: int = Field(default=60, description="每分钟请求限制")

    class Config:
        env_prefix = "SECURITY_"


class ServiceSettings(BaseSettings):
    """服务配置"""
    orchestrator_port: int = Field(default=8000, description="编排服务端口")
    host: str = Field(default="0.0.0.0", description="服务主机")
    workers: int = Field(default=1, description="工作进程数")

    class Config:
        env_prefix = "SERVICE_"


class MonitoringSettings(BaseSettings):
    """监控配置"""
    log_level: str = Field(default="INFO", description="日志级别")
    enable_metrics: bool = Field(default=True, description="启用指标收集")
    metrics_port: int = Field(default=9090, description="指标端口")

    class Config:
        env_prefix = "MONITORING_"


class DatabaseSettings(BaseSettings):
    """数据库配置"""
    # PostgreSQL配置
    use_postgresql: bool = Field(default=False, description="是否使用PostgreSQL")
    host: str = Field(default="localhost", description="数据库主机")
    port: int = Field(default=5432, description="数据库端口")
    name: str = Field(default="testgenius", description="数据库名称")
    username: str = Field(default="testgenius", description="数据库用户名")
    password: str = Field(default="password", description="数据库密码")
    
    # Redis配置
    use_redis: bool = Field(default=False, description="是否使用Redis")
    redis_host: str = Field(default="localhost", description="Redis主机")
    redis_port: int = Field(default=6379, description="Redis端口")
    redis_password: Optional[str] = Field(default=None, description="Redis密码")
    redis_db: int = Field(default=0, description="Redis数据库编号")
    
    # 兼容配置
    url: Optional[str] = Field(default=None, description="数据库URL")
    redis_url: Optional[str] = Field(default="redis://localhost:6379", description="Redis URL")

    class Config:
        env_prefix = "DATABASE_"


class AISettings(BaseSettings):
    """AI配置"""
    provider: str = Field(default="mock", description="AI提供商")
    api_key: Optional[str] = Field(default=None, description="AI API密钥")
    model_name: str = Field(default="gpt-3.5-turbo", description="模型名称")
    max_tokens: int = Field(default=4000, description="最大令牌数")
    temperature: float = Field(default=0.7, description="温度参数")

    class Config:
        env_prefix = "AI_"


class Settings(BaseSettings):
    """主配置类"""

    version: str = Field(default="0.1.0", description="应用版本")
    environment: str = Field(default="development", description="运行环境")
    debug: bool = Field(default=True, description="调试模式")

    # 子配置
    security: SecuritySettings = Field(default_factory=SecuritySettings)
    service: ServiceSettings = Field(default_factory=ServiceSettings)
    monitoring: MonitoringSettings = Field(default_factory=MonitoringSettings)
    database: DatabaseSettings = Field(default_factory=DatabaseSettings)
    ai: AISettings = Field(default_factory=AISettings)

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"

    @property
    def is_development(self) -> bool:
        return self.environment == "development"

    @property
    def is_production(self) -> bool:
        return self.environment == "production"

    def validate_production_config(self) -> List[str]:
        """验证生产环境配置"""
        errors = []

        if self.is_production:
            if self.security.secret_key == "dev-secret-key-change-in-production":
                errors.append("Production secret key must be changed")

            if "*" in self.security.cors_origins:
                errors.append("CORS origins cannot include '*' in production")

            if self.debug:
                errors.append("Debug mode must be disabled in production")

        return errors


# 全局配置实例
settings = Settings()

def get_settings() -> Settings:
    """获取配置实例"""
    return settings