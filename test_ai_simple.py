#!/usr/bin/env python3
"""
简化的AI模块测试

直接测试AI模块，避免复杂的依赖关系
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


async def test_llm_client():
    """测试LLM客户端"""
    print("🧠 测试LLM客户端...")
    
    try:
        from src.ai.llm_client import LLMClient, LLMProvider
        
        client = LLMClient()
        await client.initialize(provider=LLMProvider.MOCK)
        
        # 基础对话测试
        response = await client.chat([
            {"role": "user", "content": "你好，介绍一下TestGenius项目"}
        ])
        
        print(f"✅ LLM客户端响应: {response['content'][:100]}...")
        return True
        
    except Exception as e:
        print(f"❌ LLM客户端测试失败: {e}")
        return False


async def test_prompt_manager():
    """测试提示词管理器"""
    print("\n📝 测试提示词管理器...")
    
    try:
        from src.ai.prompt_manager import PromptManager, PromptType
        
        manager = PromptManager()
        
        # 获取系统提示词
        system_prompt = manager.get_system_prompt(PromptType.TEST_CASE_GENERATION)
        print(f"✅ 系统提示词长度: {len(system_prompt)} 字符")
        
        # 获取用户提示词
        variables = {
            'requirement_text': '用户登录功能',
            'domain': 'Web应用',
            'system_type': '电商系统'
        }
        
        user_prompt = manager.get_prompt(PromptType.TEST_CASE_GENERATION, variables)
        print(f"✅ 用户提示词长度: {len(user_prompt)} 字符")
        
        return True
        
    except Exception as e:
        print(f"❌ 提示词管理器测试失败: {e}")
        return False


async def test_structured_generation():
    """测试结构化生成"""
    print("\n📊 测试结构化生成...")
    
    try:
        from src.ai.llm_client import LLMClient, LLMProvider
        
        client = LLMClient()
        await client.initialize(provider=LLMProvider.MOCK)
        
        schema = {
            "type": "object",
            "properties": {
                "test_cases": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "title": {"type": "string"},
                            "description": {"type": "string"},
                            "priority": {"type": "string"}
                        }
                    }
                }
            }
        }
        
        result = await client.generate_structured(
            "生成电商系统用户登录功能的测试用例",
            schema
        )
        
        test_cases = result.get("test_cases", [])
        print(f"✅ 生成了 {len(test_cases)} 个结构化测试用例")
        
        for i, tc in enumerate(test_cases[:2], 1):
            print(f"   {i}. {tc.get('title', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 结构化生成测试失败: {e}")
        return False


async def test_database_integration():
    """测试与数据库的集成"""
    print("\n💾 测试数据库集成...")
    
    try:
        from src.common.database import DatabaseManager
        
        db_manager = DatabaseManager()
        await db_manager.initialize()
        
        # 模拟AI生成结果存储
        test_data = {
            "session_id": "ai_test_session",
            "requirement": "用户登录功能测试",
            "generated_count": 3,
            "generation_method": "ai_mock",
            "duration": 0.5
        }
        
        session_id = await db_manager.store_session(test_data)
        print(f"✅ AI生成结果已存储，会话ID: {session_id}")
        
        # 检索数据
        retrieved_data = await db_manager.get_session(session_id)
        print(f"✅ 数据检索成功: {retrieved_data['requirement']}")
        
        await db_manager.cleanup()
        return True
        
    except Exception as e:
        print(f"❌ 数据库集成测试失败: {e}")
        return False


async def test_ai_performance():
    """测试AI性能"""
    print("\n⚡ 测试AI性能...")
    
    try:
        from src.ai.llm_client import LLMClient, LLMProvider
        import time
        
        client = LLMClient()
        await client.initialize(provider=LLMProvider.MOCK)
        
        start_time = time.time()
        
        # 并发测试
        tasks = []
        for i in range(5):
            task = client.chat([
                {"role": "user", "content": f"生成测试场景{i+1}的测试用例"}
            ])
            tasks.append(task)
        
        responses = await asyncio.gather(*tasks)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"✅ 并发处理 {len(responses)} 个请求")
        print(f"✅ 总耗时: {duration:.2f} 秒")
        print(f"✅ 平均响应时间: {duration/len(responses):.2f} 秒")
        
        return True
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 TestGenius AI模块简化测试")
    print("=" * 50)
    
    tests = [
        test_llm_client,
        test_prompt_manager,
        test_structured_generation,
        test_database_integration,
        test_ai_performance
    ]
    
    results = []
    
    for test in tests:
        try:
            result = await test()
            results.append(result)
        except Exception as e:
            print(f"❌ 测试执行错误: {e}")
            results.append(False)
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("📋 测试结果汇总")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    print(f"✅ 通过: {passed}/{total}")
    print(f"📊 成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有AI模块测试通过！")
        print("✨ AI功能已准备就绪，可以开始UI开发")
    else:
        print(f"\n⚠️ {total-passed} 个测试失败，需要进一步调试")
    
    return passed == total


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试执行错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1) 