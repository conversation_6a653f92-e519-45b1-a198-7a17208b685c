"""
Orchestrator API 路由

提供测试用例生成、脚本生成、执行管理等RESTful接口
"""

from typing import Dict
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import StreamingResponse

from src.common.logger import get_logger
from src.orchestrator.service import OrchestratorService
from src.orchestrator.models import (
    TestCaseGenerationRequest,
    TestCaseGenerationResponse,
    ScriptGenerationRequest,
    ScriptGenerationResponse,
    ExecutionRequest,
    ExecutionResponse,
    ExecutionStatus,
    SessionContext,
    FeedbackData,
)

logger = get_logger(__name__)
router = APIRouter(tags=["orchestrator"])


def get_orchestrator_service() -> OrchestratorService:
    """获取Orchestrator服务实例的依赖注入函数"""
    # 这个函数会被FastAPI的依赖注入系统调用
    # 实际的服务实例会通过应用状态获取
    # 在main.py中会通过dependency_overrides重写这个函数
    raise HTTPException(
        status_code=503, 
        detail="Orchestrator service not available"
    )


@router.post("/sessions", response_model=SessionContext)
async def create_session(
    orchestrator: OrchestratorService = Depends(get_orchestrator_service)
) -> SessionContext:
    """
    创建新的会话上下文
    
    Returns:
        SessionContext: 新创建的会话信息
    """
    try:
        logger.info("Attempting to create session")
        session = await orchestrator.create_session()
        logger.info(f"Session created: {session.session_id}")
        return session
    except Exception as e:
        logger.error(f"Failed to create session: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to create session: {str(e)}")


@router.get("/sessions/{session_id}", response_model=SessionContext)
async def get_session(
    session_id: UUID,
    orchestrator: OrchestratorService = Depends(get_orchestrator_service)
) -> SessionContext:
    """
    获取会话信息
    
    Args:
        session_id: 会话ID
        
    Returns:
        SessionContext: 会话信息
    """
    try:
        session = await orchestrator.get_session(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        return session
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get session {session_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to get session")


@router.post("/test-cases/generate", response_model=TestCaseGenerationResponse)
async def generate_test_cases(
    request: TestCaseGenerationRequest,
    orchestrator: OrchestratorService = Depends(get_orchestrator_service)
) -> TestCaseGenerationResponse:
    """
    生成测试用例
    
    Args:
        request: 测试用例生成请求
        background_tasks: 后台任务
        orchestrator: Orchestrator服务实例
        
    Returns:
        TestCaseGenerationResponse: 测试用例生成响应
    """
    try:
        logger.info(f"Starting test case generation for session {request.session_id}")
        
        response = await orchestrator.generate_test_cases(request)
        
        logger.info(f"Test case generation completed for session {request.session_id}: {len(response.test_cases)} test cases")
        
        return response
        
    except Exception as e:
        logger.error(f"Failed to generate test cases for session {request.session_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to generate test cases")


@router.post("/scripts/generate", response_model=ScriptGenerationResponse)
async def generate_scripts(
    request: ScriptGenerationRequest,
    orchestrator: OrchestratorService = Depends(get_orchestrator_service)
) -> ScriptGenerationResponse:
    """
    生成测试脚本
    
    Args:
        request: 脚本生成请求
        background_tasks: 后台任务
        orchestrator: Orchestrator服务实例
        
    Returns:
        ScriptGenerationResponse: 脚本生成响应
    """
    try:
        logger.info(f"Starting script generation for session {request.session_id}")
        
        response = await orchestrator.generate_scripts(request)
        
        logger.info(f"Script generation completed for session {request.session_id}: {len(response.scripts)} scripts")
        
        return response
        
    except Exception as e:
        logger.error(f"Failed to generate scripts for session {request.session_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to generate scripts")


@router.post("/executions", response_model=ExecutionResponse)
async def start_execution(
    request: ExecutionRequest,
    orchestrator: OrchestratorService = Depends(get_orchestrator_service)
) -> ExecutionResponse:
    """
    启动测试执行
    
    Args:
        request: 执行请求
        background_tasks: 后台任务
        orchestrator: Orchestrator服务实例
        
    Returns:
        ExecutionResponse: 执行响应
    """
    try:
        logger.info(f"Starting test execution for session {request.session_id}")
        
        response = await orchestrator.start_execution(request)
        
        logger.info(f"Test execution started for session {request.session_id}")
        
        return response
        
    except Exception as e:
        logger.error(f"Failed to start execution for session {request.session_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to start execution")


@router.get("/executions/{execution_id}/status", response_model=ExecutionStatus)
async def get_execution_status(
    execution_id: UUID,
    orchestrator: OrchestratorService = Depends(get_orchestrator_service)
) -> ExecutionStatus:
    """
    获取执行状态
    
    Args:
        execution_id: 执行ID
        orchestrator: Orchestrator服务实例
        
    Returns:
        ExecutionStatus: 执行状态
    """
    try:
        status = await orchestrator.get_execution_status(execution_id)
        if not status:
            raise HTTPException(status_code=404, detail="Execution not found")
        return status
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get execution status for {execution_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to get execution status")


@router.get("/executions/{execution_id}/logs")
async def get_execution_logs(
    execution_id: UUID,
    orchestrator: OrchestratorService = Depends(get_orchestrator_service)
) -> StreamingResponse:
    """
    获取执行日志流
    
    Args:
        execution_id: 执行ID
        orchestrator: Orchestrator服务实例
        
    Returns:
        StreamingResponse: 日志流响应
    """
    try:
        log_stream = await orchestrator.get_execution_logs(execution_id)
        if not log_stream:
            raise HTTPException(status_code=404, detail="Execution not found")
            
        return StreamingResponse(
            log_stream,
            media_type="text/plain",
            headers={"Cache-Control": "no-cache"}
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get execution logs for {execution_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to get execution logs")


@router.post("/feedback")
async def submit_feedback(
    feedback_data: FeedbackData,
    orchestrator: OrchestratorService = Depends(get_orchestrator_service)
) -> Dict[str, str]:
    """
    提交反馈数据

    Args:
        feedback_data: 反馈数据
        orchestrator: Orchestrator服务实例

    Returns:
        Dict[str, str]: 处理结果
    """
    try:
        await orchestrator.process_feedback(feedback_data.model_dump())
        logger.info(f"Feedback submitted successfully: {feedback_data.feedback_id}")
        return {"status": "success", "message": "Feedback submitted successfully"}
    except Exception as e:
        logger.error(f"Failed to submit feedback: {e}")
        raise HTTPException(status_code=500, detail="Failed to submit feedback")


@router.get("/health")
async def health_check(
    orchestrator: OrchestratorService = Depends(get_orchestrator_service)
) -> Dict[str, str]:
    """
    API健康检查
    
    Returns:
        Dict[str, str]: 健康状态
    """
    try:
        # 如果有监控模块，返回详细健康状态
        if hasattr(orchestrator, 'system_monitor') and orchestrator.system_monitor:
            health_status = orchestrator.system_monitor.get_health_status()
            return {
                "status": health_status.get("overall_status", "unknown"),
                "component": "orchestrator-api",
                "message": "Orchestrator API is running with monitoring"
            }
        else:
            return {
                "status": "healthy",
                "component": "orchestrator-api"
            }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {
            "status": "unhealthy",
            "component": "orchestrator-api",
            "error": str(e)
        }


@router.get("/metrics")
async def get_metrics(
    name_pattern: str = None,
    limit: int = 100,
    orchestrator: OrchestratorService = Depends(get_orchestrator_service)
) -> Dict[str, any]:
    """
    获取系统指标
    
    Args:
        name_pattern: 指标名称模式筛选
        limit: 返回数据点数量限制
        
    Returns:
        Dict: 指标数据
    """
    try:
        if not hasattr(orchestrator, 'system_monitor') or not orchestrator.system_monitor:
            raise HTTPException(status_code=503, detail="Monitoring not available")
        
        metrics = orchestrator.system_monitor.get_metrics(
            name_pattern=name_pattern,
            limit=limit
        )
        
        return {
            "metrics": metrics,
            "timestamp": "2025-01-22T10:00:00Z"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get metrics: {e}")
        raise HTTPException(status_code=500, detail="Failed to get metrics")


@router.get("/health/detailed")
async def get_detailed_health(
    orchestrator: OrchestratorService = Depends(get_orchestrator_service)
) -> Dict[str, any]:
    """
    获取详细健康状态
    
    Returns:
        Dict: 详细健康状态信息
    """
    try:
        if not hasattr(orchestrator, 'system_monitor') or not orchestrator.system_monitor:
            raise HTTPException(status_code=503, detail="Monitoring not available")
        
        health_status = orchestrator.system_monitor.get_health_status()
        statistics = orchestrator.system_monitor.get_statistics()
        
        return {
            "health": health_status,
            "statistics": statistics,
            "service_info": {
                "initialized": orchestrator._initialized,
                "ready": orchestrator.is_ready(),
                "active_sessions": len(getattr(orchestrator, '_sessions', {})),
                "running_executions": len([e for e in getattr(orchestrator, '_executions', {}).values() 
                                         if hasattr(e, 'status') and e.status.value == "running"])
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get detailed health: {e}")
        raise HTTPException(status_code=500, detail="Failed to get detailed health")


@router.get("/alerts")
async def get_alerts(
    severity: str = None,
    limit: int = 50,
    orchestrator: OrchestratorService = Depends(get_orchestrator_service)
) -> Dict[str, any]:
    """
    获取告警信息
    
    Args:
        severity: 告警严重程度筛选 (warning, critical)
        limit: 返回告警数量限制
        
    Returns:
        Dict: 告警信息
    """
    try:
        if not hasattr(orchestrator, 'system_monitor') or not orchestrator.system_monitor:
            raise HTTPException(status_code=503, detail="Monitoring not available")
        
        alerts = orchestrator.system_monitor.get_alerts(
            severity=severity,
            limit=limit
        )
        
        return {
            "alerts": alerts,
            "count": len(alerts),
            "timestamp": "2025-01-22T10:00:00Z"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get alerts: {e}")
        raise HTTPException(status_code=500, detail="Failed to get alerts") 