{"modules": {"src.common.config": {"status": "✅ 成功", "path": "G:\\nifa\\TestGenius\\src\\common\\config.py", "classes": ["AISettings", "BaseSettings", "DatabaseSettings", "Field", "List", "MonitoringSettings", "Optional", "SecuritySettings", "ServiceSettings", "Settings"]}, "src.common.logger": {"status": "✅ 成功", "path": "G:\\nifa\\TestGenius\\src\\common\\logger.py", "classes": []}, "src.orchestrator.service": {"status": "❌ 失败", "error": "source code string cannot contain null bytes", "traceback": "Traceback (most recent call last):\n  File \"G:\\nifa\\TestGenius\\test_project_status.py\", line 90, in check_module_imports\n    module = importlib.import_module(module_name)\n  File \"D:\\python\\Lib\\importlib\\__init__.py\", line 88, in import_module\n    return _bootstrap._gcd_import(name[level:], package, level)\n           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"<frozen importlib._bootstrap>\", line 1387, in _gcd_import\n  File \"<frozen importlib._bootstrap>\", line 1360, in _find_and_load\n  File \"<frozen importlib._bootstrap>\", line 1310, in _find_and_load_unlocked\n  File \"<frozen importlib._bootstrap>\", line 488, in _call_with_frames_removed\n  File \"<frozen importlib._bootstrap>\", line 1387, in _gcd_import\n  File \"<frozen importlib._bootstrap>\", line 1360, in _find_and_load\n  File \"<frozen importlib._bootstrap>\", line 1331, in _find_and_load_unlocked\n  File \"<frozen importlib._bootstrap>\", line 935, in _load_unlocked\n  File \"<frozen importlib._bootstrap_external>\", line 1026, in exec_module\n  File \"<frozen importlib._bootstrap>\", line 488, in _call_with_frames_removed\n  File \"G:\\nifa\\TestGenius\\src\\orchestrator\\__init__.py\", line 7, in <module>\n    from .service import OrchestratorService\n  File \"G:\\nifa\\TestGenius\\src\\orchestrator\\service.py\", line 36, in <module>\n    from src.feedback.processor import FeedbackProcessor\nSyntaxError: source code string cannot contain null bytes\n"}, "src.orchestrator.models": {"status": "✅ 成功", "path": "G:\\nifa\\TestGenius\\src\\orchestrator\\models.py", "classes": ["Any", "BaseModel", "CryptoAlgorithm", "CryptoRequirement", "Dict", "Enum", "ExecutionRequest", "ExecutionResponse", "ExecutionResult", "ExecutionState", "ExecutionStatus", "FeedbackData", "FeedbackType", "Field", "HashAlgorithm", "List", "Optional", "ScriptDefinition", "ScriptGenerationRequest", "ScriptGenerationResponse", "SessionContext", "SignatureRequirement", "TaskStatus", "TestCaseDefinition", "TestCaseGenerationRequest", "TestCaseGenerationResponse", "UUID"]}, "src.orchestrator.api": {"status": "❌ 失败", "error": "source code string cannot contain null bytes", "traceback": "Traceback (most recent call last):\n  File \"G:\\nifa\\TestGenius\\test_project_status.py\", line 90, in check_module_imports\n    module = importlib.import_module(module_name)\n  File \"D:\\python\\Lib\\importlib\\__init__.py\", line 88, in import_module\n    return _bootstrap._gcd_import(name[level:], package, level)\n           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"<frozen importlib._bootstrap>\", line 1387, in _gcd_import\n  File \"<frozen importlib._bootstrap>\", line 1360, in _find_and_load\n  File \"<frozen importlib._bootstrap>\", line 1310, in _find_and_load_unlocked\n  File \"<frozen importlib._bootstrap>\", line 488, in _call_with_frames_removed\n  File \"<frozen importlib._bootstrap>\", line 1387, in _gcd_import\n  File \"<frozen importlib._bootstrap>\", line 1360, in _find_and_load\n  File \"<frozen importlib._bootstrap>\", line 1331, in _find_and_load_unlocked\n  File \"<frozen importlib._bootstrap>\", line 935, in _load_unlocked\n  File \"<frozen importlib._bootstrap_external>\", line 1026, in exec_module\n  File \"<frozen importlib._bootstrap>\", line 488, in _call_with_frames_removed\n  File \"G:\\nifa\\TestGenius\\src\\orchestrator\\__init__.py\", line 7, in <module>\n    from .service import OrchestratorService\n  File \"G:\\nifa\\TestGenius\\src\\orchestrator\\service.py\", line 36, in <module>\n    from src.feedback.processor import FeedbackProcessor\nSyntaxError: source code string cannot contain null bytes\n"}, "src.ai.llm_client": {"status": "✅ 成功", "path": "G:\\nifa\\TestGenius\\src\\ai\\llm_client.py", "classes": ["AIMessage", "Any", "AzureChatOpenAI", "BaseLLM", "BaseMessage", "ChatOpenAI", "ChatPromptTemplate", "Dict", "Enum", "HumanMessage", "LLMClient", "<PERSON><PERSON><PERSON><PERSON>", "List", "MockLLM", "MockResponse", "Ollama", "Optional", "SystemMessage", "Union"]}, "src.ai.prompt_manager": {"status": "✅ 成功", "path": "G:\\nifa\\TestGenius\\src\\ai\\prompt_manager.py", "classes": ["Any", "Dict", "Enum", "List", "Optional", "PromptManager", "PromptType"]}, "src.ai.test_case_ai": {"status": "✅ 成功", "path": "G:\\nifa\\TestGenius\\src\\ai\\test_case_ai.py", "classes": ["Any", "Dict", "GenerationContext", "GenerationOptions", "LLMClient", "<PERSON><PERSON><PERSON><PERSON>", "List", "Optional", "PromptManager", "PromptType", "TestCaseAI", "TestCaseDefinition"]}, "src.ai.script_gen_ai": {"status": "✅ 成功", "path": "G:\\nifa\\TestGenius\\src\\ai\\script_gen_ai.py", "classes": ["Any", "Dict", "LLMClient", "<PERSON><PERSON><PERSON><PERSON>", "List", "Optional", "PromptManager", "PromptType", "ScriptGenAI", "TestCaseDefinition"]}, "src.test_case.generator": {"status": "✅ 成功", "path": "G:\\nifa\\TestGenius\\src\\test_case\\generator.py", "classes": ["Any", "CryptoAlgorithm", "CryptoRequirement", "Dict", "GenerationContext", "GenerationOptions", "GenerationResult", "HashAlgorithm", "<PERSON><PERSON><PERSON><PERSON>", "List", "Optional", "RiskAssessment", "RiskLevel", "SignatureRequirement", "TestCaseAI", "TestCaseDefinition", "TestCaseGenerationRequest", "TestCaseGenerator", "TestCaseTemplate", "TestCaseType", "TestPriority", "UUID"]}, "src.test_case.models": {"status": "✅ 成功", "path": "G:\\nifa\\TestGenius\\src\\test_case\\models.py", "classes": ["Any", "BaseModel", "CryptoRequirement", "Dict", "Enum", "Field", "GenerationContext", "GenerationMetadata", "GenerationOptions", "GenerationResult", "List", "Optional", "RiskAssessment", "RiskLevel", "TestCaseDefinition", "TestCaseGenerationResult", "TestCaseTemplate", "TestCaseType", "TestPriority", "UUID"]}, "src.crypto.client": {"status": "✅ 成功", "path": "G:\\nifa\\TestGenius\\src\\crypto\\client.py", "classes": ["AESGCM", "Any", "CryptoClient", "CryptoOperation", "CryptoRequest", "CryptoResponse", "CryptoStats", "Dict", "EncryptionAlgorithm", "EncryptionConfig", "HashAlgorithm", "InvalidSignature", "Optional", "SignatureAlgorithm", "SignatureConfig", "UUID", "Union", "VaultConfig"]}, "src.crypto.models": {"status": "✅ 成功", "path": "G:\\nifa\\TestGenius\\src\\crypto\\models.py", "classes": ["Any", "BaseModel", "CryptoOperation", "CryptoRequest", "CryptoResponse", "CryptoStats", "Dict", "EncryptionAlgorithm", "EncryptionConfig", "Enum", "Field", "HashAlgorithm", "KeyConfig", "KeyType", "List", "Optional", "SignatureAlgorithm", "SignatureConfig", "UUID", "Union", "VaultConfig"]}}, "services": {"configuration": {"status": "✅ 正常", "config": {"version": "0.1.0", "environment": "development", "debug": true, "service_port": 8000, "log_level": "INFO", "ai_provider": "mock"}}, "ai": {"status": "✅ 正常", "llm_client": "已初始化", "prompt_templates": 4, "test_case_ai": "已初始化"}, "crypto": {"status": "✅ 正常", "client": "已初始化", "test_result": "✅ 加密测试成功"}, "orchestrator": {"status": "❌ 失败", "error": "source code string cannot contain null bytes"}}, "apis": {"rest_api": {"status": "❌ 失败", "error": "source code string cannot contain null bytes"}}, "functionality": {"test_case_generation": {"status": "✅ 正常", "generator": "已初始化", "test_cases_generated": 2, "ai_enabled": true}, "missing": {"missing_modules": [], "partial_modules": [], "functionality_status": {"实时脚本执行": "❌ 未实现", "执行结果分析": "❌ 未实现", "智能反馈处理": "❌ 未实现", "安全扫描集成": "❌ 未实现", "CI/CD集成": "❌ 未实现", "监控告警": "❌ 未实现", "数据库持久化": "❌ 未实现", "用户认证授权": "❌ 未实现", "多租户支持": "❌ 未实现", "插件系统": "❌ 未实现"}}}, "issues": ["模块导入失败: src.orchestrator.service - source code string cannot contain null bytes", "模块导入失败: src.orchestrator.api - source code string cannot contain null bytes", "Orchestrator服务错误: source code string cannot contain null bytes", "API功能错误: source code string cannot contain null bytes"], "summary": {"总体状态": "🟡 部分完成", "完成度": "82.4%", "模块状态": "11/13 成功", "服务状态": "3/4 成功", "问题数量": 4, "核心功能": {"配置系统": "✅", "AI模块": "✅", "加密模块": "✅", "测试用例生成": "✅", "编排服务": "❌", "API接口": "❌"}}}