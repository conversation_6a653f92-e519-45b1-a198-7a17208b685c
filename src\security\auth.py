"""
认证授权管理器

负责用户认证、授权、权限管理和会话控制
"""

import asyncio
import hashlib
import secrets
import jwt
from typing import Dict, List, Optional, Any, Set
from uuid import UUID, uuid4
from datetime import datetime, timedelta
from enum import Enum

from src.common.logger import get_logger


class UserRole(str, Enum):
    """用户角色"""
    ADMIN = "admin"
    DEVELOPER = "developer"
    TESTER = "tester"
    VIEWER = "viewer"
    GUEST = "guest"


class Permission(str, Enum):
    """权限定义"""
    # 会话权限
    SESSION_CREATE = "session:create"
    SESSION_VIEW = "session:view"
    SESSION_DELETE = "session:delete"
    
    # 测试用例权限
    TEST_CASE_CREATE = "test_case:create"
    TEST_CASE_VIEW = "test_case:view"
    TEST_CASE_EDIT = "test_case:edit"
    TEST_CASE_DELETE = "test_case:delete"
    
    # 脚本权限
    SCRIPT_GENERATE = "script:generate"
    SCRIPT_VIEW = "script:view"
    SCRIPT_EDIT = "script:edit"
    SCRIPT_DELETE = "script:delete"
    
    # 执行权限
    EXECUTION_START = "execution:start"
    EXECUTION_VIEW = "execution:view"
    EXECUTION_CANCEL = "execution:cancel"
    
    # 反馈权限
    FEEDBACK_SUBMIT = "feedback:submit"
    FEEDBACK_VIEW = "feedback:view"
    FEEDBACK_MANAGE = "feedback:manage"
    
    # 系统权限
    SYSTEM_CONFIG = "system:config"
    SYSTEM_MONITOR = "system:monitor"
    USER_MANAGE = "user:manage"
    
    # 加密操作权限
    CRYPTO_ENCRYPT = "crypto:encrypt"
    CRYPTO_DECRYPT = "crypto:decrypt"
    CRYPTO_SIGN = "crypto:sign"
    CRYPTO_VERIFY = "crypto:verify"


class User:
    """用户对象"""
    
    def __init__(
        self,
        user_id: UUID,
        username: str,
        email: str,
        role: UserRole,
        password_hash: str,
        salt: str,
        permissions: Optional[Set[Permission]] = None,
        created_at: Optional[datetime] = None,
        last_login: Optional[datetime] = None,
        is_active: bool = True
    ):
        self.user_id = user_id
        self.username = username
        self.email = email
        self.role = role
        self.password_hash = password_hash
        self.salt = salt
        self.permissions = permissions or set()
        self.created_at = created_at or datetime.now()
        self.last_login = last_login
        self.is_active = is_active


class AuthToken:
    """认证令牌"""
    
    def __init__(
        self,
        token_id: UUID,
        user_id: UUID,
        token: str,
        expires_at: datetime,
        created_at: Optional[datetime] = None,
        is_revoked: bool = False
    ):
        self.token_id = token_id
        self.user_id = user_id
        self.token = token
        self.expires_at = expires_at
        self.created_at = created_at or datetime.now()
        self.is_revoked = is_revoked


class AuthenticationManager:
    """
    认证授权管理器
    
    负责用户认证、授权、权限管理和会话控制
    """
    
    def __init__(self, secret_key: Optional[str] = None):
        self.logger = get_logger(__name__)
        self._initialized = False
        
        # JWT配置
        self._secret_key = secret_key or secrets.token_urlsafe(32)
        self._algorithm = "HS256"
        self._token_expiry_hours = 24
        
        # 用户存储（实际应用中应使用数据库）
        self._users: Dict[UUID, User] = {}
        self._users_by_username: Dict[str, UUID] = {}
        self._users_by_email: Dict[str, UUID] = {}
        
        # 令牌存储
        self._tokens: Dict[UUID, AuthToken] = {}
        self._active_tokens: Dict[str, UUID] = {}  # token -> token_id
        
        # 权限映射
        self._role_permissions = self._init_role_permissions()
        
        # 统计信息
        self._total_users = 0
        self._active_sessions = 0
        self._failed_logins = 0
    
    async def initialize(self) -> None:
        """初始化认证管理器"""
        try:
            self.logger.info("Initializing AuthenticationManager")
            
            # 创建默认管理员用户
            await self._create_default_admin()
            
            # 启动清理任务
            asyncio.create_task(self._cleanup_expired_tokens())
            
            self._initialized = True
            self.logger.info("AuthenticationManager initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize AuthenticationManager: {e}")
            raise
    
    async def cleanup(self) -> None:
        """清理资源"""
        try:
            self.logger.info("Cleaning up AuthenticationManager")
            
            # 清理所有会话
            self._tokens.clear()
            self._active_tokens.clear()
            
            self._initialized = False
            self.logger.info("AuthenticationManager cleaned up successfully")
            
        except Exception as e:
            self.logger.error(f"Error during AuthenticationManager cleanup: {e}")
    
    async def create_user(
        self,
        username: str,
        email: str,
        password: str,
        role: UserRole = UserRole.VIEWER,
        permissions: Optional[Set[Permission]] = None
    ) -> UUID:
        """创建用户"""
        if not self._initialized:
            raise RuntimeError("AuthenticationManager not initialized")
        
        try:
            # 检查用户名和邮箱唯一性
            if username in self._users_by_username:
                raise ValueError(f"Username '{username}' already exists")
            if email in self._users_by_email:
                raise ValueError(f"Email '{email}' already exists")
            
            # 生成用户ID
            user_id = uuid4()
            
            # 生成密码哈希
            salt = secrets.token_hex(16)
            password_hash = self._hash_password(password, salt)
            
            # 获取角色权限
            role_permissions = self._role_permissions.get(role, set())
            all_permissions = role_permissions.union(permissions or set())
            
            # 创建用户对象
            user = User(
                user_id=user_id,
                username=username,
                email=email,
                role=role,
                password_hash=password_hash,
                salt=salt,
                permissions=all_permissions
            )
            
            # 存储用户
            self._users[user_id] = user
            self._users_by_username[username] = user_id
            self._users_by_email[email] = user_id
            
            self._total_users += 1
            
            self.logger.info(f"User created: {username} ({role.value})")
            return user_id
            
        except Exception as e:
            self.logger.error(f"Failed to create user: {e}")
            raise
    
    async def authenticate(self, username: str, password: str) -> Optional[str]:
        """用户认证，返回JWT令牌"""
        if not self._initialized:
            raise RuntimeError("AuthenticationManager not initialized")
        
        try:
            # 查找用户
            user_id = self._users_by_username.get(username)
            if not user_id:
                self._failed_logins += 1
                self.logger.warning(f"Authentication failed: unknown username '{username}'")
                return None
            
            user = self._users[user_id]
            
            # 检查用户状态
            if not user.is_active:
                self._failed_logins += 1
                self.logger.warning(f"Authentication failed: user '{username}' is inactive")
                return None
            
            # 验证密码
            password_hash = self._hash_password(password, user.salt)
            if password_hash != user.password_hash:
                self._failed_logins += 1
                self.logger.warning(f"Authentication failed: invalid password for '{username}'")
                return None
            
            # 生成JWT令牌
            token = await self._generate_token(user)
            
            # 更新最后登录时间
            user.last_login = datetime.now()
            
            self.logger.info(f"User authenticated successfully: {username}")
            return token
            
        except Exception as e:
            self.logger.error(f"Authentication error: {e}")
            self._failed_logins += 1
            return None
    
    async def verify_token(self, token: str) -> Optional[User]:
        """验证JWT令牌"""
        if not self._initialized:
            raise RuntimeError("AuthenticationManager not initialized")
        
        try:
            # 检查令牌是否被撤销
            token_id = self._active_tokens.get(token)
            if token_id and token_id in self._tokens:
                auth_token = self._tokens[token_id]
                if auth_token.is_revoked:
                    return None
            
            # 解析JWT令牌
            payload = jwt.decode(token, self._secret_key, algorithms=[self._algorithm])
            
            # 提取用户信息
            user_id = UUID(payload.get("user_id"))
            exp = payload.get("exp")
            
            # 检查过期时间
            if datetime.fromtimestamp(exp) < datetime.now():
                return None
            
            # 获取用户对象
            user = self._users.get(user_id)
            if not user or not user.is_active:
                return None
            
            return user
            
        except jwt.InvalidTokenError:
            return None
        except Exception as e:
            self.logger.error(f"Token verification error: {e}")
            return None
    
    async def check_permission(self, user: User, permission: Permission) -> bool:
        """检查用户权限"""
        try:
            return permission in user.permissions
            
        except Exception as e:
            self.logger.error(f"Permission check error: {e}")
            return False
    
    async def revoke_token(self, token: str) -> bool:
        """撤销令牌"""
        try:
            token_id = self._active_tokens.get(token)
            if token_id and token_id in self._tokens:
                auth_token = self._tokens[token_id]
                auth_token.is_revoked = True
                del self._active_tokens[token]
                self._active_sessions -= 1
                return True
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to revoke token: {e}")
            return False
    
    async def update_user_password(self, user_id: UUID, new_password: str) -> bool:
        """更新用户密码"""
        try:
            user = self._users.get(user_id)
            if not user:
                return False
            
            # 生成新的盐和密码哈希
            salt = secrets.token_hex(16)
            password_hash = self._hash_password(new_password, salt)
            
            user.salt = salt
            user.password_hash = password_hash
            
            self.logger.info(f"Password updated for user: {user.username}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to update password: {e}")
            return False
    
    async def deactivate_user(self, user_id: UUID) -> bool:
        """停用用户"""
        try:
            user = self._users.get(user_id)
            if not user:
                return False
            
            user.is_active = False
            
            # 撤销用户的所有令牌
            user_tokens = [
                token for token in self._tokens.values()
                if token.user_id == user_id and not token.is_revoked
            ]
            
            for auth_token in user_tokens:
                auth_token.is_revoked = True
                if auth_token.token in self._active_tokens:
                    del self._active_tokens[auth_token.token]
                    self._active_sessions -= 1
            
            self.logger.info(f"User deactivated: {user.username}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to deactivate user: {e}")
            return False
    
    async def get_user(self, user_id: UUID) -> Optional[User]:
        """获取用户信息"""
        return self._users.get(user_id)
    
    async def get_user_by_username(self, username: str) -> Optional[User]:
        """根据用户名获取用户"""
        user_id = self._users_by_username.get(username)
        return self._users.get(user_id) if user_id else None
    
    async def list_users(self, role: Optional[UserRole] = None, active_only: bool = True) -> List[User]:
        """列出用户"""
        users = list(self._users.values())
        
        if role:
            users = [u for u in users if u.role == role]
        if active_only:
            users = [u for u in users if u.is_active]
        
        return users
    
    def _init_role_permissions(self) -> Dict[UserRole, Set[Permission]]:
        """初始化角色权限映射"""
        return {
            UserRole.ADMIN: {
                # 管理员拥有所有权限
                Permission.SESSION_CREATE, Permission.SESSION_VIEW, Permission.SESSION_DELETE,
                Permission.TEST_CASE_CREATE, Permission.TEST_CASE_VIEW, Permission.TEST_CASE_EDIT, Permission.TEST_CASE_DELETE,
                Permission.SCRIPT_GENERATE, Permission.SCRIPT_VIEW, Permission.SCRIPT_EDIT, Permission.SCRIPT_DELETE,
                Permission.EXECUTION_START, Permission.EXECUTION_VIEW, Permission.EXECUTION_CANCEL,
                Permission.FEEDBACK_SUBMIT, Permission.FEEDBACK_VIEW, Permission.FEEDBACK_MANAGE,
                Permission.SYSTEM_CONFIG, Permission.SYSTEM_MONITOR, Permission.USER_MANAGE,
                Permission.CRYPTO_ENCRYPT, Permission.CRYPTO_DECRYPT, Permission.CRYPTO_SIGN, Permission.CRYPTO_VERIFY
            },
            UserRole.DEVELOPER: {
                Permission.SESSION_CREATE, Permission.SESSION_VIEW, Permission.SESSION_DELETE,
                Permission.TEST_CASE_CREATE, Permission.TEST_CASE_VIEW, Permission.TEST_CASE_EDIT,
                Permission.SCRIPT_GENERATE, Permission.SCRIPT_VIEW, Permission.SCRIPT_EDIT,
                Permission.EXECUTION_START, Permission.EXECUTION_VIEW, Permission.EXECUTION_CANCEL,
                Permission.FEEDBACK_SUBMIT, Permission.FEEDBACK_VIEW,
                Permission.SYSTEM_MONITOR,
                Permission.CRYPTO_ENCRYPT, Permission.CRYPTO_DECRYPT, Permission.CRYPTO_SIGN, Permission.CRYPTO_VERIFY
            },
            UserRole.TESTER: {
                Permission.SESSION_CREATE, Permission.SESSION_VIEW,
                Permission.TEST_CASE_VIEW, Permission.TEST_CASE_EDIT,
                Permission.SCRIPT_VIEW,
                Permission.EXECUTION_START, Permission.EXECUTION_VIEW,
                Permission.FEEDBACK_SUBMIT, Permission.FEEDBACK_VIEW,
                Permission.CRYPTO_ENCRYPT, Permission.CRYPTO_SIGN
            },
            UserRole.VIEWER: {
                Permission.SESSION_VIEW,
                Permission.TEST_CASE_VIEW,
                Permission.SCRIPT_VIEW,
                Permission.EXECUTION_VIEW,
                Permission.FEEDBACK_VIEW
            },
            UserRole.GUEST: {
                Permission.SESSION_VIEW,
                Permission.TEST_CASE_VIEW
            }
        }
    
    async def _create_default_admin(self) -> None:
        """创建默认管理员账户"""
        try:
            if "admin" not in self._users_by_username:
                await self.create_user(
                    username="admin",
                    email="<EMAIL>",
                    password="admin123",  # 生产环境应使用强密码
                    role=UserRole.ADMIN
                )
                self.logger.info("Default admin user created")
            
        except Exception as e:
            self.logger.error(f"Failed to create default admin: {e}")
    
    async def _generate_token(self, user: User) -> str:
        """生成JWT令牌"""
        try:
            # 计算过期时间
            expires_at = datetime.now() + timedelta(hours=self._token_expiry_hours)
            
            # 创建JWT payload
            payload = {
                "user_id": str(user.user_id),
                "username": user.username,
                "role": user.role.value,
                "exp": expires_at.timestamp(),
                "iat": datetime.now().timestamp()
            }
            
            # 生成令牌
            token = jwt.encode(payload, self._secret_key, algorithm=self._algorithm)
            
            # 存储令牌信息
            token_id = uuid4()
            auth_token = AuthToken(
                token_id=token_id,
                user_id=user.user_id,
                token=token,
                expires_at=expires_at
            )
            
            self._tokens[token_id] = auth_token
            self._active_tokens[token] = token_id
            self._active_sessions += 1
            
            return token
            
        except Exception as e:
            self.logger.error(f"Failed to generate token: {e}")
            raise
    
    def _hash_password(self, password: str, salt: str) -> str:
        """哈希密码"""
        return hashlib.pbkdf2_hex(password.encode(), salt.encode(), 100000)
    
    async def _cleanup_expired_tokens(self) -> None:
        """清理过期令牌"""
        while self._initialized:
            try:
                current_time = datetime.now()
                expired_tokens = []
                
                for token_id, auth_token in self._tokens.items():
                    if auth_token.expires_at < current_time:
                        expired_tokens.append(token_id)
                
                # 清理过期令牌
                for token_id in expired_tokens:
                    auth_token = self._tokens[token_id]
                    if auth_token.token in self._active_tokens:
                        del self._active_tokens[auth_token.token]
                        self._active_sessions -= 1
                    del self._tokens[token_id]
                
                if expired_tokens:
                    self.logger.info(f"Cleaned up {len(expired_tokens)} expired tokens")
                
                # 每小时清理一次
                await asyncio.sleep(3600)
                
            except Exception as e:
                self.logger.error(f"Error during token cleanup: {e}")
                await asyncio.sleep(3600)
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        active_users = sum(1 for user in self._users.values() if user.is_active)
        
        return {
            "total_users": self._total_users,
            "active_users": active_users,
            "active_sessions": self._active_sessions,
            "failed_logins": self._failed_logins,
            "total_tokens": len(self._tokens),
            "role_distribution": {
                role.value: sum(1 for user in self._users.values() if user.role == role and user.is_active)
                for role in UserRole
            },
            "initialized": self._initialized
        } 