"""
执行分析器

负责分析测试执行结果、生成报告和提供改进建议
"""

import asyncio
import json
import re
from typing import Dict, List, Optional, Any, Union
from uuid import UUID, uuid4
from datetime import datetime
from enum import Enum
from pathlib import Path

from src.common.logger import get_logger


class AnalysisType(str, Enum):
    """分析类型"""
    EXECUTION_SUMMARY = "execution_summary"
    FAILURE_ANALYSIS = "failure_analysis"
    PERFORMANCE_ANALYSIS = "performance_analysis"
    SECURITY_ANALYSIS = "security_analysis"
    TREND_ANALYSIS = "trend_analysis"


class ReportFormat(str, Enum):
    """报告格式"""
    JSON = "json"
    HTML = "html"
    MARKDOWN = "markdown"
    PDF = "pdf"


class AnalysisResult:
    """分析结果"""
    
    def __init__(
        self,
        analysis_id: UUID,
        analysis_type: AnalysisType,
        execution_id: UUID,
        summary: str,
        details: Dict[str, Any],
        recommendations: List[str],
        severity: str = "info",
        created_at: Optional[datetime] = None
    ):
        self.analysis_id = analysis_id
        self.analysis_type = analysis_type
        self.execution_id = execution_id
        self.summary = summary
        self.details = details
        self.recommendations = recommendations
        self.severity = severity
        self.created_at = created_at or datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "analysis_id": str(self.analysis_id),
            "analysis_type": self.analysis_type.value,
            "execution_id": str(self.execution_id),
            "summary": self.summary,
            "details": self.details,
            "recommendations": self.recommendations,
            "severity": self.severity,
            "created_at": self.created_at.isoformat()
        }


class ExecutionAnalyzer:
    """
    执行分析器
    
    负责分析测试执行结果、生成报告和提供改进建议
    """
    
    def __init__(self, ai_client: Optional[Any] = None):
        self.logger = get_logger(__name__)
        self._initialized = False
        self._ai_client = ai_client
        
        # 分析结果缓存
        self._analysis_cache: Dict[UUID, AnalysisResult] = {}
        
        # 统计信息
        self._total_analyses = 0
        self._analysis_by_type: Dict[str, int] = {}
        
    async def initialize(self) -> None:
        """初始化分析器"""
        try:
            self.logger.info("Initializing ExecutionAnalyzer")
            
            # 初始化AI客户端
            if self._ai_client is None:
                try:
                    from src.ai.llm_client import LLMClient
                    self._ai_client = LLMClient()
                    await self._ai_client.initialize()
                except Exception as e:
                    self.logger.warning(f"AI client initialization failed, using fallback: {e}")
                    self._ai_client = None
            
            self._initialized = True
            self.logger.info("ExecutionAnalyzer initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize ExecutionAnalyzer: {e}")
            raise
    
    async def cleanup(self) -> None:
        """清理资源"""
        try:
            self.logger.info("Cleaning up ExecutionAnalyzer")
            
            # 清理缓存
            self._analysis_cache.clear()
            
            # 清理AI客户端
            if self._ai_client and hasattr(self._ai_client, 'cleanup'):
                await self._ai_client.cleanup()
            
            self._initialized = False
            self.logger.info("ExecutionAnalyzer cleaned up successfully")
            
        except Exception as e:
            self.logger.error(f"Error during ExecutionAnalyzer cleanup: {e}")
    
    async def analyze_execution(
        self,
        execution_id: UUID,
        execution_logs: List[Dict[str, Any]],
        analysis_types: List[AnalysisType],
        options: Optional[Dict[str, Any]] = None
    ) -> List[AnalysisResult]:
        """
        分析执行结果
        
        Args:
            execution_id: 执行ID
            execution_logs: 执行日志
            analysis_types: 分析类型列表
            options: 分析选项
            
        Returns:
            List[AnalysisResult]: 分析结果列表
        """
        if not self._initialized:
            raise RuntimeError("ExecutionAnalyzer not initialized")
        
        try:
            self.logger.info(f"Analyzing execution {execution_id} with types: {analysis_types}")
            
            results = []
            options = options or {}
            
            for analysis_type in analysis_types:
                try:
                    result = await self._perform_analysis(
                        execution_id, execution_logs, analysis_type, options
                    )
                    if result:
                        results.append(result)
                        self._analysis_cache[result.analysis_id] = result
                        
                        # 更新统计
                        self._total_analyses += 1
                        self._analysis_by_type[analysis_type.value] = (
                            self._analysis_by_type.get(analysis_type.value, 0) + 1
                        )
                        
                except Exception as e:
                    self.logger.error(f"Failed to perform {analysis_type} analysis: {e}")
            
            self.logger.info(f"Completed analysis for execution {execution_id}, generated {len(results)} results")
            return results
            
        except Exception as e:
            self.logger.error(f"Failed to analyze execution {execution_id}: {e}")
            return []
    
    async def _perform_analysis(
        self,
        execution_id: UUID,
        execution_logs: List[Dict[str, Any]],
        analysis_type: AnalysisType,
        options: Dict[str, Any]
    ) -> Optional[AnalysisResult]:
        """执行具体的分析"""
        try:
            if analysis_type == AnalysisType.EXECUTION_SUMMARY:
                return await self._analyze_execution_summary(execution_id, execution_logs, options)
            elif analysis_type == AnalysisType.FAILURE_ANALYSIS:
                return await self._analyze_failures(execution_id, execution_logs, options)
            elif analysis_type == AnalysisType.PERFORMANCE_ANALYSIS:
                return await self._analyze_performance(execution_id, execution_logs, options)
            elif analysis_type == AnalysisType.SECURITY_ANALYSIS:
                return await self._analyze_security(execution_id, execution_logs, options)
            elif analysis_type == AnalysisType.TREND_ANALYSIS:
                return await self._analyze_trends(execution_id, execution_logs, options)
            else:
                self.logger.warning(f"Unknown analysis type: {analysis_type}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error in {analysis_type} analysis: {e}")
            return None
    
    async def _analyze_execution_summary(
        self,
        execution_id: UUID,
        execution_logs: List[Dict[str, Any]],
        options: Dict[str, Any]
    ) -> Optional[AnalysisResult]:
        """分析执行概览"""
        try:
            # 统计基本信息
            total_tests = len(execution_logs)
            passed_tests = sum(1 for log in execution_logs if log.get("status") == "passed")
            failed_tests = sum(1 for log in execution_logs if log.get("status") == "failed")
            skipped_tests = sum(1 for log in execution_logs if log.get("status") == "skipped")
            
            # 计算执行时间
            start_times = [log.get("start_time") for log in execution_logs if log.get("start_time")]
            end_times = [log.get("end_time") for log in execution_logs if log.get("end_time")]
            
            total_duration = 0
            if start_times and end_times:
                start_time = min(start_times)
                end_time = max(end_times)
                if isinstance(start_time, str):
                    start_time = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
                if isinstance(end_time, str):
                    end_time = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
                total_duration = (end_time - start_time).total_seconds()
            
            # 计算成功率
            success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
            
            summary = f"执行完成：共 {total_tests} 个测试，{passed_tests} 个通过，{failed_tests} 个失败，{skipped_tests} 个跳过。成功率：{success_rate:.1f}%"
            
            details = {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": failed_tests,
                "skipped_tests": skipped_tests,
                "success_rate": success_rate,
                "total_duration": total_duration,
                "average_test_duration": total_duration / total_tests if total_tests > 0 else 0
            }
            
            recommendations = []
            if success_rate < 80:
                recommendations.append("成功率较低，建议检查失败用例的根本原因")
            if total_duration > 300:  # 5分钟
                recommendations.append("执行时间较长，建议优化测试脚本或并行执行")
            if failed_tests > 0:
                recommendations.append("存在失败用例，建议进行失败分析")
            
            severity = "error" if success_rate < 50 else "warning" if success_rate < 80 else "info"
            
            return AnalysisResult(
                analysis_id=uuid4(),
                analysis_type=AnalysisType.EXECUTION_SUMMARY,
                execution_id=execution_id,
                summary=summary,
                details=details,
                recommendations=recommendations,
                severity=severity
            )
            
        except Exception as e:
            self.logger.error(f"Failed to analyze execution summary: {e}")
            return None
    
    async def _analyze_failures(
        self,
        execution_id: UUID,
        execution_logs: List[Dict[str, Any]],
        options: Dict[str, Any]
    ) -> Optional[AnalysisResult]:
        """分析失败原因"""
        try:
            failed_logs = [log for log in execution_logs if log.get("status") == "failed"]
            
            if not failed_logs:
                return AnalysisResult(
                    analysis_id=uuid4(),
                    analysis_type=AnalysisType.FAILURE_ANALYSIS,
                    execution_id=execution_id,
                    summary="没有发现失败的测试用例",
                    details={"failed_count": 0},
                    recommendations=[],
                    severity="info"
                )
            
            # 分析失败模式
            failure_patterns = {}
            crypto_failures = 0
            network_failures = 0
            assertion_failures = 0
            timeout_failures = 0
            
            for log in failed_logs:
                error_msg = log.get("error_message", "")
                
                # 分类失败原因
                if "crypto" in error_msg.lower() or "signature" in error_msg.lower() or "encryption" in error_msg.lower():
                    crypto_failures += 1
                elif "timeout" in error_msg.lower() or "time out" in error_msg.lower():
                    timeout_failures += 1
                elif "connection" in error_msg.lower() or "network" in error_msg.lower():
                    network_failures += 1
                elif "assert" in error_msg.lower() or "expected" in error_msg.lower():
                    assertion_failures += 1
                
                # 提取失败模式
                pattern_key = self._extract_failure_pattern(error_msg)
                failure_patterns[pattern_key] = failure_patterns.get(pattern_key, 0) + 1
            
            # 使用AI分析失败原因（如果可用）
            ai_analysis = ""
            if self._ai_client:
                try:
                    ai_analysis = await self._get_ai_failure_analysis(failed_logs)
                except Exception as e:
                    self.logger.warning(f"AI failure analysis failed: {e}")
            
            summary = f"发现 {len(failed_logs)} 个失败用例，主要失败类型：加密相关 {crypto_failures}，超时 {timeout_failures}，网络问题 {network_failures}，断言错误 {assertion_failures}"
            
            details = {
                "total_failures": len(failed_logs),
                "crypto_failures": crypto_failures,
                "network_failures": network_failures,
                "assertion_failures": assertion_failures,
                "timeout_failures": timeout_failures,
                "failure_patterns": failure_patterns,
                "ai_analysis": ai_analysis
            }
            
            recommendations = []
            if crypto_failures > 0:
                recommendations.append("检查加密服务配置和密钥管理")
            if network_failures > 0:
                recommendations.append("检查网络连接和服务可用性")
            if timeout_failures > 0:
                recommendations.append("考虑增加超时时间或优化性能")
            if assertion_failures > 0:
                recommendations.append("检查测试数据和期望结果的准确性")
            
            return AnalysisResult(
                analysis_id=uuid4(),
                analysis_type=AnalysisType.FAILURE_ANALYSIS,
                execution_id=execution_id,
                summary=summary,
                details=details,
                recommendations=recommendations,
                severity="error"
            )
            
        except Exception as e:
            self.logger.error(f"Failed to analyze failures: {e}")
            return None
    
    async def _analyze_performance(
        self,
        execution_id: UUID,
        execution_logs: List[Dict[str, Any]],
        options: Dict[str, Any]
    ) -> Optional[AnalysisResult]:
        """分析性能指标"""
        try:
            durations = []
            crypto_durations = []
            
            for log in execution_logs:
                duration = log.get("duration")
                if duration and isinstance(duration, (int, float)):
                    durations.append(duration)
                
                # 提取加密操作耗时
                if "crypto_duration" in log:
                    crypto_durations.append(log["crypto_duration"])
            
            if not durations:
                return None
            
            avg_duration = sum(durations) / len(durations)
            max_duration = max(durations)
            min_duration = min(durations)
            
            # 计算性能阈值
            slow_tests = [d for d in durations if d > avg_duration * 2]
            
            summary = f"性能分析：平均耗时 {avg_duration:.2f}s，最长 {max_duration:.2f}s，最短 {min_duration:.2f}s，慢测试 {len(slow_tests)} 个"
            
            details = {
                "average_duration": avg_duration,
                "max_duration": max_duration,
                "min_duration": min_duration,
                "slow_tests_count": len(slow_tests),
                "total_tests": len(durations),
                "crypto_average_duration": sum(crypto_durations) / len(crypto_durations) if crypto_durations else 0
            }
            
            recommendations = []
            if avg_duration > 10:  # 10秒
                recommendations.append("平均执行时间较长，建议优化测试脚本")
            if len(slow_tests) > len(durations) * 0.2:  # 超过20%的测试较慢
                recommendations.append("较多慢测试，建议分析性能瓶颈")
            if crypto_durations and sum(crypto_durations) / len(crypto_durations) > 2:
                recommendations.append("加密操作耗时较长，考虑优化加密服务")
            
            severity = "warning" if avg_duration > 10 or len(slow_tests) > 5 else "info"
            
            return AnalysisResult(
                analysis_id=uuid4(),
                analysis_type=AnalysisType.PERFORMANCE_ANALYSIS,
                execution_id=execution_id,
                summary=summary,
                details=details,
                recommendations=recommendations,
                severity=severity
            )
            
        except Exception as e:
            self.logger.error(f"Failed to analyze performance: {e}")
            return None
    
    async def _analyze_security(
        self,
        execution_id: UUID,
        execution_logs: List[Dict[str, Any]],
        options: Dict[str, Any]
    ) -> Optional[AnalysisResult]:
        """分析安全相关指标"""
        try:
            security_events = []
            crypto_events = []
            auth_events = []
            
            for log in execution_logs:
                log_message = log.get("message", "").lower()
                
                # 检测安全相关事件
                if any(keyword in log_message for keyword in ["signature", "encrypt", "decrypt", "hash"]):
                    crypto_events.append(log)
                if any(keyword in log_message for keyword in ["auth", "token", "unauthorized", "forbidden"]):
                    auth_events.append(log)
                if log.get("level") == "ERROR" and any(keyword in log_message for keyword in ["security", "attack", "invalid"]):
                    security_events.append(log)
            
            # 检查加密操作成功率
            crypto_success_rate = 100
            if crypto_events:
                crypto_failures = [e for e in crypto_events if e.get("status") == "failed"]
                crypto_success_rate = ((len(crypto_events) - len(crypto_failures)) / len(crypto_events)) * 100
            
            summary = f"安全分析：加密事件 {len(crypto_events)} 个，认证事件 {len(auth_events)} 个，安全告警 {len(security_events)} 个，加密成功率 {crypto_success_rate:.1f}%"
            
            details = {
                "crypto_events_count": len(crypto_events),
                "auth_events_count": len(auth_events),
                "security_events_count": len(security_events),
                "crypto_success_rate": crypto_success_rate
            }
            
            recommendations = []
            if crypto_success_rate < 100:
                recommendations.append("存在加密操作失败，请检查密钥配置和加密服务")
            if security_events:
                recommendations.append("发现安全告警，建议详细检查相关事件")
            if len(auth_events) > 0:
                recommendations.append("检查认证相关事件，确保访问控制正常")
            
            severity = "error" if security_events else "warning" if crypto_success_rate < 100 else "info"
            
            return AnalysisResult(
                analysis_id=uuid4(),
                analysis_type=AnalysisType.SECURITY_ANALYSIS,
                execution_id=execution_id,
                summary=summary,
                details=details,
                recommendations=recommendations,
                severity=severity
            )
            
        except Exception as e:
            self.logger.error(f"Failed to analyze security: {e}")
            return None
    
    async def _analyze_trends(
        self,
        execution_id: UUID,
        execution_logs: List[Dict[str, Any]],
        options: Dict[str, Any]
    ) -> Optional[AnalysisResult]:
        """分析趋势（基于历史数据）"""
        try:
            # 这是一个简化的趋势分析，实际应该基于历史数据
            current_success_rate = 0
            total_tests = len(execution_logs)
            if total_tests > 0:
                passed_tests = sum(1 for log in execution_logs if log.get("status") == "passed")
                current_success_rate = (passed_tests / total_tests) * 100
            
            # 模拟趋势数据（实际应该从数据库获取）
            historical_success_rate = 85.0  # 模拟历史平均成功率
            trend = "improving" if current_success_rate > historical_success_rate else "declining"
            
            summary = f"趋势分析：当前成功率 {current_success_rate:.1f}%，历史平均 {historical_success_rate:.1f}%，趋势：{trend}"
            
            details = {
                "current_success_rate": current_success_rate,
                "historical_success_rate": historical_success_rate,
                "trend": trend,
                "trend_percentage": abs(current_success_rate - historical_success_rate)
            }
            
            recommendations = []
            if trend == "declining":
                recommendations.append("成功率呈下降趋势，建议分析最近的变更影响")
            elif trend == "improving":
                recommendations.append("成功率有所提升，建议保持当前的改进措施")
            
            return AnalysisResult(
                analysis_id=uuid4(),
                analysis_type=AnalysisType.TREND_ANALYSIS,
                execution_id=execution_id,
                summary=summary,
                details=details,
                recommendations=recommendations,
                severity="info"
            )
            
        except Exception as e:
            self.logger.error(f"Failed to analyze trends: {e}")
            return None
    
    def _extract_failure_pattern(self, error_message: str) -> str:
        """提取失败模式"""
        # 简化的模式提取逻辑
        if not error_message:
            return "unknown"
        
        # 移除具体的值和路径，保留模式
        pattern = re.sub(r'\d+', 'N', error_message)
        pattern = re.sub(r'["\'].*?["\']', '"VALUE"', pattern)
        pattern = re.sub(r'/[\w/]+', '/PATH', pattern)
        
        return pattern[:100]  # 限制长度
    
    async def _get_ai_failure_analysis(self, failed_logs: List[Dict[str, Any]]) -> str:
        """使用AI分析失败原因"""
        try:
            if not self._ai_client or not hasattr(self._ai_client, 'generate_completion'):
                return ""
            
            # 构建分析prompt
            failures_text = "\n".join([
                f"测试: {log.get('test_name', 'unknown')}, 错误: {log.get('error_message', 'no error message')}"
                for log in failed_logs[:10]  # 限制数量
            ])
            
            prompt = f"""
            请分析以下测试失败的原因，并提供改进建议：
            
            失败的测试：
            {failures_text}
            
            请提供：
            1. 主要失败原因分析
            2. 可能的根本原因
            3. 具体的改进建议
            """
            
            response = await self._ai_client.generate_completion(prompt)
            return response.get("content", "")
            
        except Exception as e:
            self.logger.error(f"AI failure analysis error: {e}")
            return ""
    
    async def generate_report(
        self,
        analysis_results: List[AnalysisResult],
        report_format: ReportFormat = ReportFormat.JSON,
        output_path: Optional[Path] = None
    ) -> Union[str, Dict[str, Any]]:
        """
        生成分析报告
        
        Args:
            analysis_results: 分析结果列表
            report_format: 报告格式
            output_path: 输出路径（可选）
            
        Returns:
            Union[str, Dict[str, Any]]: 报告内容
        """
        try:
            if report_format == ReportFormat.JSON:
                report = {
                    "generated_at": datetime.now().isoformat(),
                    "total_analyses": len(analysis_results),
                    "analyses": [result.to_dict() for result in analysis_results],
                    "summary": self._generate_report_summary(analysis_results)
                }
                
                if output_path:
                    with open(output_path, 'w', encoding='utf-8') as f:
                        json.dump(report, f, indent=2, ensure_ascii=False)
                
                return report
                
            elif report_format == ReportFormat.MARKDOWN:
                return self._generate_markdown_report(analysis_results, output_path)
                
            elif report_format == ReportFormat.HTML:
                return self._generate_html_report(analysis_results, output_path)
                
            else:
                raise ValueError(f"Unsupported report format: {report_format}")
                
        except Exception as e:
            self.logger.error(f"Failed to generate report: {e}")
            return {}
    
    def _generate_report_summary(self, analysis_results: List[AnalysisResult]) -> Dict[str, Any]:
        """生成报告摘要"""
        total_recommendations = sum(len(result.recommendations) for result in analysis_results)
        severity_counts = {}
        for result in analysis_results:
            severity_counts[result.severity] = severity_counts.get(result.severity, 0) + 1
        
        return {
            "total_analyses": len(analysis_results),
            "total_recommendations": total_recommendations,
            "severity_distribution": severity_counts,
            "analysis_types": list(set(result.analysis_type.value for result in analysis_results))
        }
    
    def _generate_markdown_report(self, analysis_results: List[AnalysisResult], output_path: Optional[Path] = None) -> str:
        """生成Markdown格式报告"""
        lines = [
            "# 测试执行分析报告",
            f"\n生成时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"\n## 概览\n",
            f"- 分析项目数：{len(analysis_results)}",
            f"- 建议总数：{sum(len(result.recommendations) for result in analysis_results)}",
            "\n## 详细分析\n"
        ]
        
        for result in analysis_results:
            lines.extend([
                f"### {result.analysis_type.value.replace('_', ' ').title()}",
                f"\n**严重级别：** {result.severity}",
                f"\n**摘要：** {result.summary}",
                "\n**建议：**\n"
            ])
            
            for rec in result.recommendations:
                lines.append(f"- {rec}")
            
            lines.append("\n---\n")
        
        content = "\n".join(lines)
        
        if output_path:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(content)
        
        return content
    
    def _generate_html_report(self, analysis_results: List[AnalysisResult], output_path: Optional[Path] = None) -> str:
        """生成HTML格式报告"""
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>测试执行分析报告</title>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
                .analysis {{ margin: 20px 0; padding: 15px; border-left: 4px solid #007cba; background-color: #f9f9f9; }}
                .error {{ border-left-color: #d32f2f; }}
                .warning {{ border-left-color: #ff9800; }}
                .info {{ border-left-color: #4caf50; }}
                .recommendations {{ margin-top: 10px; }}
                .recommendations li {{ margin: 5px 0; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>测试执行分析报告</h1>
                <p>生成时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                <p>分析项目数：{len(analysis_results)}</p>
            </div>
        """
        
        for result in analysis_results:
            html_content += f"""
            <div class="analysis {result.severity}">
                <h2>{result.analysis_type.value.replace('_', ' ').title()}</h2>
                <p><strong>严重级别：</strong> {result.severity}</p>
                <p><strong>摘要：</strong> {result.summary}</p>
            """
            
            if result.recommendations:
                html_content += "<div class='recommendations'><strong>建议：</strong><ul>"
                for rec in result.recommendations:
                    html_content += f"<li>{rec}</li>"
                html_content += "</ul></div>"
            
            html_content += "</div>"
        
        html_content += """
        </body>
        </html>
        """
        
        if output_path:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
        
        return html_content
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            "total_analyses": self._total_analyses,
            "analysis_by_type": self._analysis_by_type,
            "cached_results": len(self._analysis_cache),
            "initialized": self._initialized
        } 