<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TestGenius - AI智能测试用例生成平台</title>
    <link href="https://cdn.tailwindcss.com/3.4.0/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        .loading-spinner {
            border: 3px solid #f3f3f4;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 导航栏 -->
    <nav class="gradient-bg shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <h1 class="text-2xl font-bold text-white">
                            <i class="fas fa-robot mr-2"></i>TestGenius
                        </h1>
                    </div>
                    <div class="hidden md:ml-10 md:flex md:space-x-8">
                        <a href="#" class="text-white hover:text-gray-200 px-3 py-2 rounded-md text-sm font-medium">
                            <i class="fas fa-home mr-1"></i>首页
                        </a>
                        <a href="#" class="text-white hover:text-gray-200 px-3 py-2 rounded-md text-sm font-medium">
                            <i class="fas fa-flask mr-1"></i>测试用例
                        </a>
                        <a href="#" class="text-white hover:text-gray-200 px-3 py-2 rounded-md text-sm font-medium">
                            <i class="fas fa-chart-bar mr-1"></i>报告
                        </a>
                        <a href="#" class="text-white hover:text-gray-200 px-3 py-2 rounded-md text-sm font-medium">
                            <i class="fas fa-cog mr-1"></i>设置
                        </a>
                    </div>
                </div>
                <div class="flex items-center">
                    <div class="ml-3 relative">
                        <div class="flex items-center space-x-4">
                            <span class="text-white text-sm">
                                <i class="fas fa-circle text-green-400 mr-1"></i>服务正常
                            </span>
                            <button class="text-white hover:text-gray-200">
                                <i class="fas fa-user-circle text-xl"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- 欢迎区域 -->
        <div class="px-4 py-6 sm:px-0">
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h2 class="text-2xl font-bold text-gray-900">欢迎使用 TestGenius</h2>
                            <p class="mt-1 text-sm text-gray-600">
                                AI驱动的智能测试用例生成平台，让测试更简单、更高效
                            </p>
                        </div>
                        <div class="flex-shrink-0">
                            <i class="fas fa-magic text-4xl text-indigo-600"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速操作卡片 -->
        <div class="px-4 py-6 sm:px-0">
            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                <!-- 生成测试用例 -->
                <div class="bg-white overflow-hidden shadow rounded-lg card-hover cursor-pointer" onclick="showTestCaseGenerator()">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-plus-circle text-3xl text-green-600"></i>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">
                                        生成测试用例
                                    </dt>
                                    <dd class="text-lg font-medium text-gray-900">
                                        AI智能生成
                                    </dd>
                                </dl>
                            </div>
                        </div>
                        <div class="mt-4">
                            <p class="text-sm text-gray-600">
                                基于需求描述，使用AI生成高质量的测试用例
                            </p>
                        </div>
                    </div>
                </div>

                <!-- 查看历史 -->
                <div class="bg-white overflow-hidden shadow rounded-lg card-hover cursor-pointer" onclick="showHistory()">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-history text-3xl text-blue-600"></i>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">
                                        历史记录
                                    </dt>
                                    <dd class="text-lg font-medium text-gray-900">
                                        查看和管理
                                    </dd>
                                </dl>
                            </div>
                        </div>
                        <div class="mt-4">
                            <p class="text-sm text-gray-600">
                                查看之前生成的测试用例和执行结果
                            </p>
                        </div>
                    </div>
                </div>

                <!-- 系统状态 -->
                <div class="bg-white overflow-hidden shadow rounded-lg card-hover cursor-pointer" onclick="showSystemStatus()">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-server text-3xl text-purple-600"></i>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">
                                        系统状态
                                    </dt>
                                    <dd class="text-lg font-medium text-gray-900">
                                        监控面板
                                    </dd>
                                </dl>
                            </div>
                        </div>
                        <div class="mt-4">
                            <p class="text-sm text-gray-600">
                                查看系统运行状态和性能指标
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 测试用例生成器模态框 -->
        <div id="testCaseModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
            <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-medium text-gray-900">
                            <i class="fas fa-magic mr-2 text-indigo-600"></i>AI测试用例生成器
                        </h3>
                        <button onclick="closeModal('testCaseModal')" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                    
                    <form id="testCaseForm" onsubmit="generateTestCases(event)">
                        <div class="space-y-4">
                            <!-- 需求描述 -->
                            <div>
                                <label for="requirement" class="block text-sm font-medium text-gray-700">
                                    需求描述 <span class="text-red-500">*</span>
                                </label>
                                <textarea 
                                    id="requirement" 
                                    name="requirement" 
                                    rows="4" 
                                    class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                                    placeholder="请详细描述需要测试的功能需求..."
                                    required
                                ></textarea>
                            </div>

                            <!-- 项目信息 -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="domain" class="block text-sm font-medium text-gray-700">应用领域</label>
                                    <select id="domain" name="domain" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500">
                                        <option value="web">Web应用</option>
                                        <option value="mobile">移动应用</option>
                                        <option value="api">API接口</option>
                                        <option value="desktop">桌面应用</option>
                                        <option value="other">其他</option>
                                    </select>
                                </div>
                                <div>
                                    <label for="systemType" class="block text-sm font-medium text-gray-700">系统类型</label>
                                    <select id="systemType" name="systemType" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500">
                                        <option value="ecommerce">电商系统</option>
                                        <option value="finance">金融系统</option>
                                        <option value="education">教育系统</option>
                                        <option value="healthcare">医疗系统</option>
                                        <option value="general">通用系统</option>
                                    </select>
                                </div>
                            </div>

                            <!-- 生成选项 -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">测试类型</label>
                                <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
                                    <label class="flex items-center">
                                        <input type="checkbox" name="testTypes" value="functional" checked class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                        <span class="ml-2 text-sm">功能测试</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="testTypes" value="security" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                        <span class="ml-2 text-sm">安全测试</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="testTypes" value="performance" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                        <span class="ml-2 text-sm">性能测试</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="testTypes" value="boundary" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                        <span class="ml-2 text-sm">边界测试</span>
                                    </label>
                                </div>
                            </div>

                            <!-- 高级选项 -->
                            <div class="border-t pt-4">
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <div>
                                        <label for="maxTestCases" class="block text-sm font-medium text-gray-700">最大生成数量</label>
                                        <input type="number" id="maxTestCases" name="maxTestCases" value="10" min="1" max="50" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500">
                                    </div>
                                    <div class="flex items-center">
                                        <input type="checkbox" id="includeCrypto" name="includeCrypto" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                        <label for="includeCrypto" class="ml-2 text-sm font-medium text-gray-700">包含加密测试</label>
                                    </div>
                                    <div class="flex items-center">
                                        <input type="checkbox" id="generateScript" name="generateScript" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                        <label for="generateScript" class="ml-2 text-sm font-medium text-gray-700">生成脚本代码</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-end space-x-3 mt-6">
                            <button type="button" onclick="closeModal('testCaseModal')" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500">
                                取消
                            </button>
                            <button type="submit" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500" id="generateBtn">
                                <i class="fas fa-magic mr-2"></i>生成测试用例
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 结果显示区域 -->
        <div id="resultsSection" class="px-4 py-6 sm:px-0 hidden">
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                        <i class="fas fa-list-alt mr-2 text-green-600"></i>生成的测试用例
                    </h3>
                    <div id="testCaseResults">
                        <!-- 测试用例结果将在这里显示 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // 全局变量
        let currentSession = null;
        const API_BASE = 'http://localhost:8000';

        // 显示测试用例生成器
        function showTestCaseGenerator() {
            document.getElementById('testCaseModal').classList.remove('hidden');
        }

        // 关闭模态框
        function closeModal(modalId) {
            document.getElementById(modalId).classList.add('hidden');
        }

        // 显示历史记录
        function showHistory() {
            alert('历史记录功能开发中...');
        }

        // 显示系统状态
        function showSystemStatus() {
            alert('系统状态功能开发中...');
        }

        // 生成测试用例
        async function generateTestCases(event) {
            event.preventDefault();
            
            const generateBtn = document.getElementById('generateBtn');
            const originalText = generateBtn.innerHTML;
            
            // 显示加载状态
            generateBtn.innerHTML = '<div class="loading-spinner inline-block mr-2"></div>正在生成...';
            generateBtn.disabled = true;

            try {
                // 收集表单数据
                const formData = new FormData(event.target);
                const testTypes = [];
                formData.getAll('testTypes').forEach(type => testTypes.push(type));

                const requestData = {
                    requirement_text: formData.get('requirement'),
                    domain: formData.get('domain'),
                    system_type: formData.get('systemType'),
                    test_types: testTypes,
                    max_test_cases: parseInt(formData.get('maxTestCases')),
                    include_crypto: formData.get('includeCrypto') === 'on',
                    generate_script: formData.get('generateScript') === 'on'
                };

                // 调用API
                const response = await fetch(`${API_BASE}/api/v1/test-cases/generate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                
                // 显示结果
                displayTestCaseResults(result);
                closeModal('testCaseModal');
                
                // 显示成功消息
                showNotification('测试用例生成成功！', 'success');

            } catch (error) {
                console.error('生成测试用例失败:', error);
                showNotification('生成失败，请检查网络连接或稍后重试', 'error');
            } finally {
                // 恢复按钮状态
                generateBtn.innerHTML = originalText;
                generateBtn.disabled = false;
            }
        }

        // 显示测试用例结果
        function displayTestCaseResults(result) {
            const resultsSection = document.getElementById('resultsSection');
            const testCaseResults = document.getElementById('testCaseResults');
            
            if (!result.test_cases || result.test_cases.length === 0) {
                testCaseResults.innerHTML = '<p class="text-gray-500">没有生成任何测试用例</p>';
                resultsSection.classList.remove('hidden');
                return;
            }

            let html = `
                <div class="mb-4 p-4 bg-blue-50 rounded-lg">
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="font-medium text-blue-900">生成统计</h4>
                            <p class="text-sm text-blue-700">
                                共生成 ${result.test_cases.length} 个测试用例
                                ${result.generation_time ? `，耗时 ${result.generation_time.toFixed(2)} 秒` : ''}
                            </p>
                        </div>
                        <div class="flex space-x-2">
                            <button onclick="exportTestCases('json')" class="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700">
                                <i class="fas fa-download mr-1"></i>导出JSON
                            </button>
                            <button onclick="exportTestCases('excel')" class="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700">
                                <i class="fas fa-file-excel mr-1"></i>导出Excel
                            </button>
                        </div>
                    </div>
                </div>
            `;

            html += '<div class="space-y-4">';
            
            result.test_cases.forEach((testCase, index) => {
                html += `
                    <div class="border rounded-lg p-4 hover:shadow-md transition-shadow">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <h5 class="font-medium text-gray-900 mb-2">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800 mr-2">
                                        TC-${String(index + 1).padStart(3, '0')}
                                    </span>
                                    ${testCase.title || '未命名测试用例'}
                                </h5>
                                
                                <p class="text-sm text-gray-600 mb-3">${testCase.description || '无描述'}</p>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <h6 class="font-medium text-gray-700 mb-2">测试步骤:</h6>
                                        <ol class="list-decimal list-inside text-sm text-gray-600 space-y-1">
                                            ${(testCase.steps || []).map(step => `<li>${step}</li>`).join('')}
                                        </ol>
                                    </div>
                                    
                                    <div>
                                        <h6 class="font-medium text-gray-700 mb-2">预期结果:</h6>
                                        <p class="text-sm text-gray-600">${testCase.expected_result || '未定义'}</p>
                                        
                                        ${testCase.test_data ? `
                                            <h6 class="font-medium text-gray-700 mt-3 mb-2">测试数据:</h6>
                                            <div class="text-sm text-gray-600 bg-gray-50 p-2 rounded">
                                                <pre>${JSON.stringify(testCase.test_data, null, 2)}</pre>
                                            </div>
                                        ` : ''}
                                    </div>
                                </div>
                                
                                <div class="flex items-center justify-between mt-4 pt-3 border-t">
                                    <div class="flex space-x-2">
                                        ${(testCase.tags || []).map(tag => 
                                            `<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">${tag}</span>`
                                        ).join('')}
                                    </div>
                                    
                                    <div class="flex items-center space-x-2 text-sm text-gray-500">
                                        <span class="inline-flex items-center">
                                            <i class="fas fa-flag mr-1"></i>
                                            ${testCase.priority || 'Medium'}
                                        </span>
                                        <span class="inline-flex items-center">
                                            <i class="fas fa-tag mr-1"></i>
                                            ${testCase.type || 'Functional'}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
            
            testCaseResults.innerHTML = html;
            resultsSection.classList.remove('hidden');
            
            // 滚动到结果区域
            resultsSection.scrollIntoView({ behavior: 'smooth' });
        }

        // 导出测试用例
        function exportTestCases(format) {
            // 这里实现导出功能
            alert(`${format.toUpperCase()} 导出功能开发中...`);
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 p-4 rounded-md shadow-lg z-50 ${
                type === 'success' ? 'bg-green-500 text-white' :
                type === 'error' ? 'bg-red-500 text-white' :
                'bg-blue-500 text-white'
            }`;
            notification.innerHTML = `
                <div class="flex items-center">
                    <i class="fas ${
                        type === 'success' ? 'fa-check-circle' :
                        type === 'error' ? 'fa-exclamation-circle' :
                        'fa-info-circle'
                    } mr-2"></i>
                    <span>${message}</span>
                </div>
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 5000);
        }

        // 页面加载完成后检查API连接
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                if (response.ok) {
                    console.log('API连接正常');
                } else {
                    showNotification('API服务连接异常', 'error');
                }
            } catch (error) {
                console.error('API连接失败:', error);
                showNotification('无法连接到后端服务，请确保服务已启动', 'error');
            }
        });
    </script>
</body>
</html> 