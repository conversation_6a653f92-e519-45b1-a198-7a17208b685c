#!/usr/bin/env python3
"""
数据库管理器测试脚本

测试 DatabaseManager 的各项功能
"""

import asyncio
import sys
import os
from uuid import uuid4
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.common.database import DatabaseManager, DataRecord


async def test_database_manager():
    """测试数据库管理器"""
    print("=" * 60)
    print("测试数据库管理器功能")
    print("=" * 60)
    
    # 初始化数据库管理器
    print("\n1. 初始化数据库管理器...")
    db_manager = DatabaseManager()
    await db_manager.initialize()
    
    if not db_manager.is_ready():
        print("❌ 数据库管理器未准备就绪")
        return
    
    print("✅ 数据库管理器初始化成功")
    
    # 健康检查
    print("\n2. 执行健康检查...")
    health_status = await db_manager.health_check()
    print(f"健康状态: {health_status['status']}")
    print(f"数据库类型: {health_status['database_type']}")
    print(f"连接状态: {health_status['connection_status']}")
    
    # 测试会话存储
    print("\n3. 测试会话存储...")
    session_data = {
        "user_id": "test_user",
        "environment": "test",
        "started_at": datetime.now().isoformat(),
        "context": {
            "domain": "测试域",
            "project": "TestGenius"
        }
    }
    
    session_id = await db_manager.store_session(session_data)
    print(f"✅ 会话已存储，ID: {session_id}")
    
    # 获取会话
    retrieved_session = await db_manager.get_session(session_id)
    if retrieved_session:
        print(f"✅ 会话检索成功: {retrieved_session['user_id']}")
    else:
        print("❌ 会话检索失败")
    
    # 更新会话
    updates = {"last_activity": datetime.now().isoformat(), "status": "active"}
    update_success = await db_manager.update_session(session_id, updates)
    if update_success:
        print("✅ 会话更新成功")
    else:
        print("❌ 会话更新失败")
    
    # 测试测试用例存储
    print("\n4. 测试测试用例存储...")
    test_case_data = {
        "title": "用户登录测试",
        "description": "验证用户登录功能",
        "steps": ["打开登录页", "输入用户名密码", "点击登录"],
        "expected_result": "成功登录",
        "priority": "high",
        "created_by": "test_user"
    }
    
    test_case_id = await db_manager.store_test_case(test_case_data)
    print(f"✅ 测试用例已存储，ID: {test_case_id}")
    
    # 获取测试用例
    retrieved_test_case = await db_manager.get_test_case(test_case_id)
    if retrieved_test_case:
        print(f"✅ 测试用例检索成功: {retrieved_test_case['title']}")
    else:
        print("❌ 测试用例检索失败")
    
    # 测试执行记录存储
    print("\n5. 测试执行记录存储...")
    execution_data = {
        "test_case_id": str(test_case_id),
        "session_id": str(session_id),
        "status": "completed",
        "result": "passed",
        "duration": 45.5,
        "log_data": {"stdout": "Test completed successfully", "stderr": ""},
        "environment": {"python_version": "3.9", "os": "windows"}
    }
    
    execution_id = await db_manager.store_execution_record(execution_data)
    print(f"✅ 执行记录已存储，ID: {execution_id}")
    
    # 测试缓存功能
    print("\n6. 测试缓存功能...")
    cache_key = "test_cache_key"
    cache_value = {"data": "test_cache_value", "timestamp": datetime.now().isoformat()}
    
    # 设置缓存
    cache_set_success = db_manager.cache_set(cache_key, cache_value, ttl=60)
    if cache_set_success:
        print("✅ 缓存设置成功")
    
    # 获取缓存
    cached_value = db_manager.cache_get(cache_key)
    if cached_value and cached_value["data"] == cache_value["data"]:
        print("✅ 缓存检索成功")
    else:
        print("❌ 缓存检索失败")
    
    # 测试批量操作
    print("\n7. 测试批量操作...")
    batch_records = []
    for i in range(3):
        record = DataRecord(
            record_id=uuid4(),
            record_type="test_batch",
            data={"batch_index": i, "value": f"batch_value_{i}"}
        )
        batch_records.append(("test_batch", record))
    
    batch_ids = await db_manager.batch_store_records(batch_records)
    print(f"✅ 批量存储成功，存储了 {len(batch_ids)} 条记录")
    
    # 批量获取
    batch_requests = [("test_batch", record_id) for record_id in batch_ids]
    batch_results = await db_manager.batch_get_records(batch_requests)
    successful_retrievals = sum(1 for result in batch_results if result is not None)
    print(f"✅ 批量检索成功，检索了 {successful_retrievals} 条记录")
    
    # 测试查询功能
    print("\n8. 测试查询功能...")
    query_results = await db_manager.query_records("test_batch", limit=2)
    print(f"✅ 查询成功，返回 {len(query_results)} 条记录")
    
    # 数据完整性检查
    print("\n9. 执行数据完整性检查...")
    integrity_report = await db_manager.verify_data_integrity()
    print(f"完整性状态: {integrity_report['status']}")
    if integrity_report['issues']:
        print(f"发现问题: {len(integrity_report['issues'])}")
        for issue in integrity_report['issues'][:3]:  # 只显示前3个问题
            print(f"  - {issue}")
    else:
        print("✅ 数据完整性良好")
    
    # 统计信息
    print("\n10. 获取统计信息...")
    stats = db_manager.get_statistics()
    print(f"总读取次数: {stats['total_reads']}")
    print(f"总写入次数: {stats['total_writes']}")
    print(f"缓存命中率: {stats['cache_hit_rate']}%")
    print(f"内存中记录总数: {stats['total_memory_records']}")
    
    # 备份数据
    print("\n11. 测试数据备份...")
    backup_file = await db_manager.backup_data("test_backup.json")
    print(f"✅ 数据备份成功: {backup_file}")
    
    # 清理测试
    print("\n12. 清理资源...")
    await db_manager.cleanup()
    print("✅ 资源清理完成")
    
    print("\n" + "=" * 60)
    print("数据库管理器测试完成")
    print("=" * 60)


if __name__ == "__main__":
    try:
        asyncio.run(test_database_manager())
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc() 