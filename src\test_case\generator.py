"""
测试用例生成器

基于需求文本和上下文，使用大模型和检索系统生成结构化测试用例
"""

import asyncio
import time
from typing import Dict, List, Optional, Any
from uuid import UUID

from src.common.logger import get_logger
from .models import (
    TestCaseTemplate,
    GenerationContext,
    GenerationOptions,
    GenerationResult,
    RiskAssessment,
    RiskLevel,
    TestPriority,
    TestCaseType,
)

logger = get_logger(__name__)


class TestCaseGenerator:
    """
    测试用例生成器
    
    基于需求描述和上下文生成结构化的测试用例，现在集成了AI能力
    """
    
    def __init__(self):
        self.templates = {}
        self.ai_generator = None  # 延迟初始化
        self.ai_enabled = False
        self._load_templates()
        
    async def initialize(self, enable_ai: bool = True, ai_provider: str = "MOCK"):
        """
        初始化生成器
        
        Args:
            enable_ai: 是否启用AI生成
            ai_provider: AI服务提供商
        """
        logger.info("Initializing TestCaseGenerator")
        
        if enable_ai:
            try:
                # 延迟导入以避免循环导入
                from src.ai.test_case_ai import TestCaseAI
                from src.ai.llm_client import LLMProvider
                
                self.ai_generator = TestCaseAI()
                provider_enum = getattr(LLMProvider, ai_provider, LLMProvider.MOCK)
                await self.ai_generator.initialize(provider=provider_enum)
                self.ai_enabled = True
                logger.info("AI-powered test case generation enabled")
            except Exception as e:
                logger.warning(f"Failed to initialize AI generator, falling back to rule-based: {e}")
                self.ai_enabled = False
        else:
            self.ai_enabled = False
            
        logger.info("TestCaseGenerator initialized successfully")
    
    async def generate_test_cases(
        self,
        requirement_text: str,
        context: GenerationContext,
        options: Optional[GenerationOptions] = None
    ) -> List[Any]:
        """
        生成测试用例
        
        Args:
            requirement_text: 需求文本
            context: 生成上下文
            options: 生成选项
            
        Returns:
            List[TestCaseDefinition]: 生成的测试用例列表
        """
        start_time = time.time()
        
        try:
            logger.info(
                f"Starting test case generation for session {context.session_id}"
            )
            
            # 设置默认选项
            if options is None:
                options = GenerationOptions()
                
            if context is None:
                context = GenerationContext()
            
            # 选择生成方法
            if self.ai_enabled and self.ai_generator.is_available():
                logger.info("Using AI-powered test case generation")
                test_cases = await self.ai_generator.generate_test_cases(
                    requirement_text, context, options
                )
                ai_enhanced = True
            else:
                logger.info("Using rule-based test case generation")
                test_cases = await self._fallback_generation(
                    requirement_text, context, options
                )
                ai_enhanced = False
            
            # 进行风险评估
            if options.risk_assessment_enabled:
                await self._assess_risks(test_cases, context)
            
            generation_time = time.time() - start_time
            
            logger.info(
                f"Test case generation completed for session {context.session_id}: "
                f"{len(test_cases)} test cases generated in {generation_time:.2f}s "
                f"using {'ai' if ai_enhanced else 'rule-based'} method"
            )
            
            return test_cases
            
        except Exception as e:
            logger.error(
                f"Test case generation failed for session {context.session_id}: {e}"
            )
            raise

    async def _fallback_generation(
        self,
        requirement_text: str,
        context: GenerationContext,
        options: GenerationOptions
    ) -> List[Any]:
        """
        降级生成方法（规则引擎）
        
        Args:
            requirement_text: 需求描述
            context: 生成上下文
            options: 生成选项
            
        Returns:
            生成的测试用例列表
        """
        # 解析需求文本
        parsed_requirements = await self._parse_requirements(
            requirement_text, context
        )
        
        # 生成测试用例
        return await self._generate_test_cases_impl(
            parsed_requirements, context, options
        )

    def _load_templates(self):
        """加载默认模板"""
        self.templates = {
            'functional': {
                'name': '功能测试模板',
                'description': '标准功能测试用例模板',
                'steps': [
                    '访问功能页面',
                    '输入测试数据',
                    '执行功能操作',
                    '验证操作结果'
                ]
            },
            'security': {
                'name': '安全测试模板',
                'description': '安全性测试用例模板',
                'steps': [
                    '配置安全测试环境',
                    '执行安全扫描',
                    '分析安全漏洞',
                    '验证防护机制'
                ]
            }
        }
        logger.info(f"Loaded {len(self.templates)} default templates")

    def _calculate_complexity(self, text: str) -> str:
        """计算需求复杂度"""
        word_count = len(text.split())
        if word_count < 50:
            return "low"
        elif word_count < 200:
            return "medium"
        else:
            return "high"

    async def _parse_requirements(
        self, requirement_text: str, context: GenerationContext
    ) -> Dict[str, Any]:
        """
        解析需求文本
        
        Args:
            requirement_text: 需求文本
            context: 生成上下文
            
        Returns:
            Dict[str, Any]: 解析后的需求信息
        """
        # TODO: 使用大模型解析需求文本
        # 这里先返回简单的解析结果
        
        return {
            "functional_requirements": [
                "用户登录功能",
                "数据查询功能", 
                "报表生成功能"
            ],
            "non_functional_requirements": [
                "响应时间不超过2秒",
                "支持1000并发用户",
                "数据安全传输"
            ],
            "security_requirements": [
                "用户身份验证",
                "数据加密传输",
                "访问权限控制"
            ],
            "interfaces": [
                "REST API",
                "Web UI",
                "数据库接口"
            ],
            "data_entities": [
                "用户信息",
                "业务数据",
                "日志数据"
            ]
        }
    
    async def _generate_test_cases_impl(
        self,
        parsed_requirements: Dict[str, Any],
        context: GenerationContext,
        options: GenerationOptions
    ) -> List[Any]:
        """
        实现测试用例生成逻辑
        
        Args:
            parsed_requirements: 解析后的需求
            context: 生成上下文
            options: 生成选项
            
        Returns:
            List[TestCaseDefinition]: 生成的测试用例
        """
        test_cases = []
        
        # 生成功能测试用例
        functional_cases = await self._generate_functional_test_cases(
            parsed_requirements.get("functional_requirements", []),
            context, options
        )
        test_cases.extend(functional_cases)
        
        # 生成边界测试用例
        if options.include_boundary_tests:
            boundary_cases = await self._generate_boundary_test_cases(
                parsed_requirements, context, options
            )
            test_cases.extend(boundary_cases)
        
        # 生成异常测试用例
        if options.include_exception_tests:
            exception_cases = await self._generate_exception_test_cases(
                parsed_requirements, context, options
            )
            test_cases.extend(exception_cases)
        
        # 生成安全测试用例
        if options.include_security_tests:
            security_cases = await self._generate_security_test_cases(
                parsed_requirements, context, options
            )
            test_cases.extend(security_cases)
        
        # 限制测试用例数量
        if len(test_cases) > options.max_test_cases:
            # 按优先级排序并截取
            test_cases = sorted(
                test_cases, 
                key=lambda x: self._get_priority_weight(x.priority),
                reverse=True
            )[:options.max_test_cases]
        
        return test_cases
    
    async def _generate_functional_test_cases(
        self,
        functional_requirements: List[str],
        context: GenerationContext,
        options: GenerationOptions
    ) -> List[Any]:
        """生成功能测试用例"""
        test_cases = []
        
        # 延迟导入以避免循环导入
        from src.orchestrator.models import TestCaseDefinition, CryptoRequirement, SignatureRequirement, CryptoAlgorithm, HashAlgorithm
        
        for req in functional_requirements:
            test_case = TestCaseDefinition(
                title=f"测试{req}",
                description=f"验证{req}的正常功能",
                scenario=f"用户执行{req}操作",
                preconditions=["系统正常运行", "用户已登录"],
                test_steps=[
                    f"访问{req}功能页面",
                    f"输入有效的{req}参数",
                    f"执行{req}操作",
                    "验证操作结果"
                ],
                expected_results=[
                    f"{req}功能正常响应",
                    "返回预期结果",
                    "界面显示正确"
                ],
                test_data={
                    "input_data": {"param1": "valid_value", "param2": "test_data"},
                    "expected_output": {"status": "success", "result": "expected"}
                },
                priority="high",
                tags=["functional", "basic"],
                risk_level="medium"
            )
            
            # 添加加密需求
            if context.crypto_enabled:
                test_case.crypto_requirements = CryptoRequirement(
                    enabled=True,
                    algorithm=CryptoAlgorithm.AES_GCM,
                    key_id="test_key_001",
                    fields=["sensitive_data", "user_info"]
                )
                test_case.signature_requirements = SignatureRequirement(
                    enabled=True,
                    algorithm="RSA-PSS",
                    key_id="sign_key_001",
                    hash_algorithm=HashAlgorithm.SHA256
                )
                test_case.tags.append("crypto")
            
            test_cases.append(test_case)
        
        return test_cases
    
    async def _generate_boundary_test_cases(
        self, parsed_requirements: Dict[str, Any], context: GenerationContext,
        options: GenerationOptions
    ) -> List[Any]:
        """生成边界测试用例"""
        test_cases = []
        
        # 延迟导入以避免循环导入
        from src.orchestrator.models import TestCaseDefinition
        
        # 生成边界值测试用例
        boundary_case = TestCaseDefinition(
            title="边界值测试",
            description="验证系统在边界条件下的行为",
            scenario="使用边界值进行系统测试",
            preconditions=["系统正常运行"],
            test_steps=[
                "准备边界值测试数据",
                "执行边界值测试",
                "验证系统响应"
            ],
            expected_results=[
                "系统正确处理边界值",
                "不发生异常错误"
            ],
            test_data={
                "boundary_values": {
                    "min_value": 0,
                    "max_value": 999999,
                    "null_value": None,
                    "empty_string": "",
                    "max_length_string": "a" * 1000
                }
            },
            priority="medium",
            tags=["boundary", "edge_case"],
            risk_level="medium"
        )
        
        test_cases.append(boundary_case)
        return test_cases
    
    async def _generate_exception_test_cases(
        self, parsed_requirements: Dict[str, Any], context: GenerationContext,
        options: GenerationOptions
    ) -> List[Any]:
        """生成异常测试用例"""
        test_cases = []
        
        # 延迟导入以避免循环导入
        from src.orchestrator.models import TestCaseDefinition
        
        # 生成异常处理测试用例
        exception_case = TestCaseDefinition(
            title="异常处理测试",
            description="验证系统异常处理机制",
            scenario="模拟异常情况测试系统稳定性",
            preconditions=["系统正常运行"],
            test_steps=[
                "构造异常输入数据",
                "执行异常操作",
                "验证异常处理结果"
            ],
            expected_results=[
                "系统正确捕获异常",
                "返回适当的错误信息",
                "系统保持稳定"
            ],
            test_data={
                "invalid_inputs": {
                    "invalid_format": "invalid_data_format",
                    "sql_injection": "'; DROP TABLE users; --",
                    "xss_payload": "<script>alert('xss')</script>",
                    "buffer_overflow": "A" * 10000
                }
            },
            priority="high",
            tags=["exception", "negative"],
            risk_level="high"
        )
        
        test_cases.append(exception_case)
        return test_cases
    
    async def _generate_security_test_cases(
        self, parsed_requirements: Dict[str, Any], context: GenerationContext,
        options: GenerationOptions
    ) -> List[Any]:
        """生成安全测试用例"""
        test_cases = []
        
        # 延迟导入以避免循环导入
        from src.orchestrator.models import TestCaseDefinition
        
        # 生成安全测试用例
        security_case = TestCaseDefinition(
            title="安全性测试",
            description="验证系统安全防护机制",
            scenario="执行安全渗透测试",
            preconditions=["系统正常运行", "安全测试环境"],
            test_steps=[
                "执行身份验证测试",
                "执行权限控制测试",
                "执行数据加密测试",
                "验证安全机制有效性"
            ],
            expected_results=[
                "身份验证机制正常",
                "权限控制有效",
                "数据传输加密",
                "防护机制生效"
            ],
            test_data={
                "security_tests": {
                    "auth_bypass": ["admin", "password123"],
                    "privilege_escalation": "user_to_admin",
                    "data_leakage": "sensitive_data_check"
                }
            },
            priority="critical",
            tags=["security", "penetration"],
            risk_level="critical"
        )
        
        # 如果启用了加密，添加加密相关的安全测试
        if context.crypto_enabled:
            security_case.crypto_requirements = CryptoRequirement(
                enabled=True,
                algorithm=CryptoAlgorithm.SM4,
                key_id="security_test_key",
                fields=["all_data"]
            )
            security_case.signature_requirements = SignatureRequirement(
                enabled=True,
                algorithm="SM2",
                key_id="security_sign_key",
                hash_algorithm=HashAlgorithm.SM3
            )
        
        test_cases.append(security_case)
        return test_cases
    
    async def _assess_risks(
        self, test_cases: List[Any], context: GenerationContext
    ) -> None:
        """评估测试用例风险"""
        for test_case in test_cases:
            # 简单的风险评估逻辑
            risk_score = 0.5  # 基础风险分数
            
            # 根据标签调整风险分数
            if "security" in test_case.tags:
                risk_score += 0.3
            if "crypto" in test_case.tags:
                risk_score += 0.2
            if "exception" in test_case.tags:
                risk_score += 0.1
            
            # 根据优先级调整风险分数
            if test_case.priority == "critical":
                risk_score += 0.2
            elif test_case.priority == "high":
                risk_score += 0.1
            
            # 限制风险分数范围
            risk_score = min(1.0, max(0.0, risk_score))
            
            # 设置风险等级
            if risk_score >= 0.8:
                test_case.risk_level = "critical"
            elif risk_score >= 0.6:
                test_case.risk_level = "high"
            elif risk_score >= 0.3:
                test_case.risk_level = "medium"
            else:
                test_case.risk_level = "low"
    
    def _get_priority_weight(self, priority: str) -> int:
        """获取优先级权重"""
        weights = {
            "critical": 4,
            "high": 3,
            "medium": 2,
            "low": 1
        }
        return weights.get(priority, 1) 