"""
Orchestrator 服务类

实现核心编排逻辑，协调各个模块完成测试用例生成、脚本生成和执行管理
"""

import asyncio
from typing import Dict, List, Optional, AsyncGenerator, Any
from uuid import UUID, uuid4
from datetime import datetime
import threading
import os

from src.common.config import settings
from src.common.logger import get_logger
from src.orchestrator.models import (
    SessionContext,
    TestCaseGenerationRequest,
    TestCaseGenerationResponse,
    ScriptGenerationRequest,
    ScriptGenerationResponse,
    ExecutionRequest,
    ExecutionResponse,
    ExecutionStatus,
    ExecutionState,
    TaskStatus,
    TestCaseDefinition,
    ScriptDefinition,
)
from src.test_case.generator import TestCaseGenerator
from src.ai.llm_client import LLMProvider
from src.ai.llm_client import LLMClient
from src.crypto.client import CryptoClient
from src.script_gen.template_engine import TemplateEngine
from src.scheduler.scheduler import ExecutionScheduler
from src.feedback.processor import FeedbackProcessor
from src.analysis.analyzer import ExecutionAnalyzer
from src.scheduler.executor import ScriptExecutor
from src.security.auth import AuthenticationManager

logger = get_logger(__name__)


class OrchestratorService:
    """
    Orchestrator 服务类
    
    负责协调各个模块，管理会话上下文，处理业务流程
    """
    
    def __init__(self):
        self.logger = get_logger(__name__)
        # TODO: 实现专用的AI和Crypto日志适配器
        self.ai_logger = self.logger
        self.crypto_logger = self.logger

        # 初始化测试用例生成器 - 延迟初始化避免循环依赖
        self._test_case_generator = None

        # 会话存储 (生产环境应使用Redis或数据库)
        self._sessions: Dict[UUID, SessionContext] = {}

        # 任务存储 (生产环境应使用Redis或数据库)
        self._tasks: Dict[UUID, Dict[str, Any]] = {}

        # 执行状态存储 (生产环境应使用Redis或数据库)
        self._executions: Dict[UUID, ExecutionStatus] = {}

        # 后台任务跟踪 - 防止内存泄漏
        self._background_tasks: Dict[UUID, asyncio.Task] = {}

        # 线程安全锁 - 在初始化时创建
        self._session_lock = None
        self._execution_lock = None

        # 服务状态
        self._initialized = False
        self._ready = False

        # 模块引用 - 延迟初始化
        self.script_engine = None
        self.scheduler = None
        self.crypto_client = None
        self.execution_analyzer = None
        self.feedback_processor = None
        self.plugin_manager = None
        self.system_monitor = None
        self.database_manager = None
        self.auth_manager = None
        self.script_executor = None
        
    async def initialize(self) -> None:
        """
        初始化服务
        """
        try:
            self.logger.info("Initializing Orchestrator service")

            # 初始化锁对象
            self._session_lock = asyncio.Lock()
            self._execution_lock = asyncio.Lock()

            # 初始化各个模块连接
            await self._initialize_modules()

            # 初始化数据存储
            await self._initialize_storage()

            # 验证配置
            await self._validate_configuration()

            # 加载插件 (设计文档3.1要求)
            await self._load_plugins()

            self._initialized = True
            self._ready = True

            self.logger.info("Orchestrator service initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize Orchestrator service: {e}")
            # 清理部分初始化的资源
            await self._cleanup_partial_initialization()
            raise
            
    async def _load_plugins(self):
        """加载插件 (设计文档3.1要求)"""
        try:
            # 在测试环境或开发环境中跳过插件加载
            if settings.environment in ["test", "development"]:
                self.logger.info(f"Skipping plugin loading in {settings.environment} mode")
                self.plugin_manager = None
                return

            # 导入插件管理器
            from src.common.plugin_manager import PluginManager
            plugin_dir = getattr(settings, 'plugin_dir', 'src/plugins')

            self.logger.info(f"Initializing plugin manager with directory: {plugin_dir}")
            self.plugin_manager = PluginManager(plugin_dir)

            # 初始化插件管理器
            await self.plugin_manager.initialize()

            # 加载插件
            await self.plugin_manager.load_plugins()

            active_plugins = self.plugin_manager.list_active_plugins()
            self.logger.info(f"Successfully loaded {len(active_plugins)} plugins: {active_plugins}")

        except ImportError as e:
            self.logger.warning(f"Plugin manager not available: {e}")
            self.plugin_manager = None
        except Exception as e:
            self.logger.error(f"Failed to load plugins: {e}")
            # 在插件加载失败时不阻止系统启动，只记录错误
            self.plugin_manager = None
            self.logger.warning("Continuing without plugins due to loading failure")
    
    async def cleanup(self) -> None:
        """
        清理资源
        """
        try:
            self.logger.info("Cleaning up Orchestrator service")

            # 停止所有运行中的任务
            await self._stop_running_tasks()

            # 清理连接
            await self._cleanup_connections()

            # 卸载插件 (设计文档3.1要求)
            if hasattr(self, 'plugin_manager') and self.plugin_manager is not None:
                try:
                    await self.plugin_manager.unload_plugins()
                    self.logger.info("Plugins unloaded successfully")
                except Exception as e:
                    self.logger.error(f"Failed to unload plugins: {e}")

            # 清理加密客户端
            if hasattr(self, 'crypto_client') and self.crypto_client is not None:
                try:
                    await self.crypto_client.cleanup()
                    self.logger.info("Crypto client cleaned up")
                except Exception as e:
                    self.logger.error(f"Failed to cleanup crypto client: {e}")

            # 清理其他模块
            await self._cleanup_modules()

            self._ready = False
            self._initialized = False

            self.logger.info("Orchestrator service cleaned up successfully")

        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")

    async def _cleanup_partial_initialization(self) -> None:
        """清理部分初始化的资源"""
        try:
            self.logger.info("Cleaning up partial initialization")

            # 清理已初始化的模块
            if self.crypto_client:
                await self.crypto_client.cleanup()

            if self.scheduler:
                await self.scheduler.cleanup()

            if self.script_engine:
                await self.script_engine.cleanup()

            if self._test_case_generator:
                await self._test_case_generator.cleanup()

            # 清理任务和会话
            self._background_tasks.clear()
            self._sessions.clear()
            self._tasks.clear()
            self._executions.clear()

        except Exception as e:
            self.logger.error(f"Error during partial cleanup: {e}")

    async def _cleanup_modules(self) -> None:
        """清理所有模块"""
        modules_to_cleanup = [
            ('llm_client', getattr(self, 'llm_client', None)),
            ('crypto_client', getattr(self, 'crypto_client', None)),
            ('test_case_generator', getattr(self, 'test_case_generator', None)),
            ('template_engine', getattr(self, 'template_engine', None)),
            ('script_executor', getattr(self, 'script_executor', None)),
            ('feedback_processor', getattr(self, 'feedback_processor', None)),
            ('execution_analyzer', getattr(self, 'execution_analyzer', None)),
            ('auth_manager', getattr(self, 'auth_manager', None)),
            ('scheduler', getattr(self, 'scheduler', None)),
            ('system_monitor', getattr(self, 'system_monitor', None)),
            ('database_manager', getattr(self, 'database_manager', None))
        ]

        for module_name, module in modules_to_cleanup:
            if module is not None:
                try:
                    if hasattr(module, 'cleanup'):
                        await module.cleanup()
                    self.logger.debug(f"{module_name} cleaned up successfully")
                except Exception as e:
                    self.logger.error(f"Failed to cleanup {module_name}: {e}")
    
    def is_ready(self) -> bool:
        """
        检查服务是否就绪
        
        Returns:
            bool: 服务就绪状态
        """
        return self._ready
    
    async def create_session(self) -> SessionContext:
        """
        创建新的会话上下文
        
        Returns:
            SessionContext: 新创建的会话
        """
        session = SessionContext()

        # 线程安全的会话创建
        async with self._session_lock:
            self._sessions[session.session_id] = session

        self.logger.info(
            f"Session created: {session.session_id} at {session.created_at.isoformat()}"
        )

        return session
    
    async def get_session(self, session_id: UUID) -> Optional[SessionContext]:
        """
        获取会话信息
        
        Args:
            session_id: 会话ID
            
        Returns:
            Optional[SessionContext]: 会话信息，如果不存在则返回None
        """
        return self._sessions.get(session_id)
    
    async def generate_test_cases(
        self, request: TestCaseGenerationRequest
    ) -> TestCaseGenerationResponse:
        """
        生成测试用例

        Args:
            request: 测试用例生成请求

        Returns:
            TestCaseGenerationResponse: 测试用例生成响应
        """
        # 检查服务是否就绪
        if not self.is_ready():
            raise RuntimeError("Orchestrator service is not ready")

        task_id = uuid4()

        self.ai_logger.info(
            f"Starting AI operation: test_case_generation for session {request.session_id}, task {task_id}"
        )

        try:
            # 更新会话上下文 (线程安全)
            if self._session_lock is None:
                raise RuntimeError("Service not properly initialized")

            async with self._session_lock:
                session = await self.get_session(request.session_id)
                if not session:
                    raise ValueError(f"Session {request.session_id} not found")

                session.updated_at = datetime.now()

            # 调用测试用例生成模块
            test_cases = await self._generate_test_cases_impl(request)

            response = TestCaseGenerationResponse(
                task_id=task_id,
                session_id=request.session_id,
                status=TaskStatus.COMPLETED,
                test_cases=test_cases,
                metadata={
                    "requirement_length": len(request.requirement_text),
                    "crypto_enabled": request.crypto_enabled,
                    "generation_time": datetime.now().isoformat()
                }
            )

            # 存储任务结果 (线程安全)
            async with self._session_lock:
                self._tasks[task_id] = {
                    "type": "test_case_generation",
                    "request": request.model_dump(),
                    "response": response.model_dump(),
                    "created_at": datetime.now()
                }

            self.ai_logger.info(
                f"AI operation completed: test_case_generation for session {request.session_id}, task {task_id}, generated {len(test_cases)} test cases"
            )

            return response

        except Exception as e:
            self.logger.error(
                f"Test case generation failed for session {request.session_id}, task {task_id}: {e}"
            )

            self.ai_logger.error(
                f"AI operation failed: test_case_generation for session {request.session_id}, task {task_id}: {e}"
            )

            raise
    
    async def generate_scripts(
        self, request: ScriptGenerationRequest
    ) -> ScriptGenerationResponse:
        """
        生成测试脚本
        
        Args:
            request: 脚本生成请求
            
        Returns:
            ScriptGenerationResponse: 脚本生成响应
        """
        task_id = uuid4()
        
        self.ai_logger.info(
            f"Starting AI operation: script_generation for session {request.session_id}, task {task_id}"
        )
        
        try:
            # 更新会话上下文
            session = await self.get_session(request.session_id)
            if not session:
                raise ValueError(f"Session {request.session_id} not found")
            
            session.updated_at = datetime.now()
            
            # 调用脚本生成模块 (暂时使用模拟数据)
            scripts = await self._generate_scripts_impl(request)
            
            response = ScriptGenerationResponse(
                task_id=task_id,
                session_id=request.session_id,
                status=TaskStatus.COMPLETED,
                scripts=scripts,
                metadata={
                    "test_case_count": len(request.test_case_ids),
                    "target_language": request.target_language,
                    "target_framework": request.target_framework,
                    "generation_time": datetime.now().isoformat()
                }
            )
            
            # 存储任务结果
            self._tasks[task_id] = {
                "type": "script_generation",
                "request": request.model_dump(),
                "response": response.model_dump(),
                "created_at": datetime.now()
            }
            
            return response
            
        except Exception as e:
            self.logger.error(
                f"Script generation failed for session {request.session_id}, task {task_id}: {e}"
            )
            raise
    
    async def start_execution(
        self, request: ExecutionRequest
    ) -> ExecutionResponse:
        """
        启动测试执行
        
        Args:
            request: 执行请求
            
        Returns:
            ExecutionResponse: 执行响应
        """
        execution_id = uuid4()
        
        try:
            # 创建执行状态
            execution_status = ExecutionStatus(
                execution_id=execution_id,
                session_id=request.session_id,
                status=ExecutionState.QUEUED,
                total_scripts=len(request.script_ids),
                start_time=datetime.now()
            )
            
            self._executions[execution_id] = execution_status

            # 启动后台执行任务并跟踪
            task = asyncio.create_task(self._execute_scripts_background(execution_id, request))
            self._background_tasks[execution_id] = task

            # 添加任务完成回调以清理
            task.add_done_callback(lambda t: self._cleanup_background_task(execution_id, t))
            
            response = ExecutionResponse(
                execution_id=execution_id,
                session_id=request.session_id,
                status=ExecutionState.QUEUED,
                script_count=len(request.script_ids)
            )
            
            self.logger.info(
                f"Test execution started: {execution_id} for session {request.session_id}, {len(request.script_ids)} scripts"
            )
            
            return response
            
        except Exception as e:
            self.logger.error(
                f"Failed to start execution {execution_id} for session {request.session_id}: {e}"
            )
            raise
    
    async def get_execution_status(self, execution_id: UUID) -> Optional[ExecutionStatus]:
        """
        获取执行状态
        
        Args:
            execution_id: 执行ID
            
        Returns:
            Optional[ExecutionStatus]: 执行状态，如果不存在则返回None
        """
        return self._executions.get(execution_id)
    
    async def get_execution_logs(self, execution_id: UUID) -> Optional[AsyncGenerator[str, None]]:
        """
        获取执行日志流
        
        Args:
            execution_id: 执行ID
            
        Returns:
            Optional[AsyncGenerator[str, None]]: 日志流，如果不存在则返回None
        """
        execution = self._executions.get(execution_id)
        if not execution:
            return None
        
        # 模拟日志流
        async def log_generator():
            for i in range(10):
                yield f"Log line {i} for execution {execution_id}\n"
                await asyncio.sleep(0.1)
        
        return log_generator()
    
    async def process_feedback(self, feedback_data: Dict[str, Any]) -> None:
        """
        处理反馈数据 (设计文档3.7要求)
        
        Args:
            feedback_data: 反馈数据
        """
        try:
            self.logger.info(
                "Processing feedback",
                session_id=feedback_data.get('session_id'),
                feedback_type=feedback_data.get('type')
            )
            
            # 如果有反馈处理器，使用它处理反馈
            if self.feedback_processor:
                try:
                    from src.feedback.processor import FeedbackType, FeedbackSeverity
                    
                    # 转换反馈数据格式
                    feedback_type_str = feedback_data.get('type', 'general_feedback')
                    try:
                        feedback_type = FeedbackType(feedback_type_str)
                    except ValueError:
                        feedback_type = FeedbackType.GENERAL_FEEDBACK
                    
                    severity_str = feedback_data.get('severity', 'medium')
                    try:
                        severity = FeedbackSeverity(severity_str)
                    except ValueError:
                        severity = FeedbackSeverity.MEDIUM
                    
                    # 提交反馈到处理器
                    feedback_id = await self.feedback_processor.submit_feedback(
                        feedback_type=feedback_type,
                        title=feedback_data.get('title', 'User Feedback'),
                        description=feedback_data.get('description', ''),
                        source_id=feedback_data.get('source_id'),
                        severity=severity,
                        metadata=feedback_data.get('metadata', {}),
                        submitter=feedback_data.get('submitter')
                    )
                    
                    self.logger.info(f"Feedback submitted to processor with ID: {feedback_id}")
                    
                except Exception as e:
                    self.logger.error(f"Failed to process feedback with processor: {e}")
                    # 降级到旧的处理方式
                    await self._update_knowledge_base_from_feedback(feedback_data)
            else:
                # 降级到旧的处理方式
                await self._update_knowledge_base_from_feedback(feedback_data)
            
            self.logger.info("Feedback processed successfully")
            
        except Exception as e:
            self.logger.error(f"Feedback processing failed: {e}")
            raise
        
    async def _update_knowledge_base_from_feedback(self, feedback_data: Dict) -> None:
        """使用反馈更新知识库 (设计文档3.7)"""
        # 调用知识库API或本地服务
        # TODO: 替换为真实集成
        self.logger.info(
            f"Updating knowledge base from feedback type {feedback_data.get('type')}"
        )
    
    # 私有方法
    
    async def _initialize_modules(self) -> None:
        """初始化所有模块"""
        try:
            self.logger.info("Initializing modules")

            # 初始化AI模块
            try:
                self.llm_client = LLMClient()
                await self.llm_client.initialize()
                self.logger.info("LLM client initialized successfully")
            except Exception as e:
                self.logger.warning(f"LLM client initialization failed: {e}")
                self.llm_client = None

            # 初始化加密模块
            try:
                self.crypto_client = CryptoClient()
                await self.crypto_client.initialize()
                self.logger.info("Crypto client initialized successfully")
            except Exception as e:
                self.logger.warning(f"Crypto client initialization failed: {e}")
                self.crypto_client = None

            # 初始化测试用例生成器
            try:
                self.test_case_generator = TestCaseGenerator(
                    ai_client=self.llm_client,
                    crypto_client=self.crypto_client
                )
                await self.test_case_generator.initialize()
                self.logger.info("Test case generator initialized successfully")
            except Exception as e:
                self.logger.warning(f"Test case generator initialization failed: {e}")
                self.test_case_generator = None

            # 初始化脚本生成器
            try:
                from src.script_gen.template_engine import TemplateEngine
                self.template_engine = TemplateEngine()
                await self.template_engine.initialize()
                self.logger.info("Template engine initialized successfully")
            except Exception as e:
                self.logger.warning(f"Template engine initialization failed: {e}")
                self.template_engine = None

            # 初始化反馈模块 (设计文档3.7要求)
            try:
                from src.feedback.processor import FeedbackProcessor
                self.feedback_processor = FeedbackProcessor(ai_client=self.llm_client)
                await self.feedback_processor.initialize()
                self.logger.info("Feedback processor initialized successfully")
            except ImportError as e:
                self.logger.warning(f"Feedback processor not available: {e}")
                self.feedback_processor = None
            except Exception as e:
                self.logger.error(f"Failed to initialize feedback processor: {e}")
                self.feedback_processor = None

            # 初始化分析模块 (设计文档3.6要求)
            try:
                from src.analysis.analyzer import ExecutionAnalyzer
                self.execution_analyzer = ExecutionAnalyzer(ai_client=self.llm_client)
                await self.execution_analyzer.initialize()
                self.logger.info("Execution analyzer initialized successfully")
            except ImportError as e:
                self.logger.warning(f"Execution analyzer not available: {e}")
                self.execution_analyzer = None
            except Exception as e:
                self.logger.error(f"Failed to initialize execution analyzer: {e}")
                self.execution_analyzer = None

            # 初始化脚本执行器
            try:
                from src.scheduler.executor import ScriptExecutor
                self.script_executor = ScriptExecutor()
                await self.script_executor.initialize()
                self.logger.info("Script executor initialized successfully")
            except ImportError as e:
                self.logger.warning(f"Script executor not available: {e}")
                self.script_executor = None
            except Exception as e:
                self.logger.error(f"Failed to initialize script executor: {e}")
                self.script_executor = None

            # 初始化认证管理器
            try:
                from src.security.auth import AuthenticationManager
                jwt_secret = os.getenv("JWT_SECRET_KEY", "your-secret-key-change-in-production")
                self.auth_manager = AuthenticationManager(jwt_secret_key=jwt_secret)
                await self.auth_manager.initialize()
                self.logger.info("Authentication manager initialized successfully")
            except ImportError as e:
                self.logger.warning(f"Authentication manager not available: {e}")
                self.auth_manager = None
            except Exception as e:
                self.logger.error(f"Failed to initialize authentication manager: {e}")
                self.auth_manager = None

            # 初始化模板引擎
            try:
                self.scheduler = ExecutionScheduler()
                await self.scheduler.initialize()
                self.logger.info("Execution scheduler initialized successfully")
            except Exception as e:
                self.logger.warning(f"Execution scheduler initialization failed: {e}")
                self.scheduler = None

            # 初始化监控模块
            try:
                from src.common.monitor import SystemMonitor
                self.system_monitor = SystemMonitor(collection_interval=30)  # 30秒收集一次
                await self.system_monitor.initialize()
                
                # 注册自定义健康检查
                self._register_orchestrator_health_checks()
                
                self.logger.info("System monitor initialized successfully")
            except ImportError as e:
                self.logger.warning(f"System monitor not available: {e}")
                self.system_monitor = None
            except Exception as e:
                self.logger.error(f"Failed to initialize system monitor: {e}")
                self.system_monitor = None

            # 初始化数据库管理器
            try:
                from src.common.database import DatabaseManager
                self.database_manager = DatabaseManager(settings=self.settings)
                await self.database_manager.initialize()
                self.logger.info("Database manager initialized successfully")
            except ImportError as e:
                self.logger.warning(f"Database manager not available: {e}")
                self.database_manager = None
            except Exception as e:
                self.logger.error(f"Failed to initialize database manager: {e}")
                self.database_manager = None

            # 初始化认证管理器
            try:
                from src.security.auth import AuthenticationManager
                jwt_secret = getattr(self.settings.security, 'secret_key', 'dev-secret-key-change-in-production')
                self.auth_manager = AuthenticationManager(jwt_secret_key=jwt_secret)
                await self.auth_manager.initialize()
                self.logger.info("Authentication manager initialized successfully")
            except ImportError as e:
                self.logger.warning(f"Authentication manager not available: {e}")
                self.auth_manager = None
            except Exception as e:
                self.logger.error(f"Failed to initialize authentication manager: {e}")
                self.auth_manager = None

            self.logger.info("All modules initialized")

        except Exception as e:
            self.logger.error(f"Failed to initialize modules: {e}")
            raise
    
    async def _initialize_storage(self) -> None:
        """初始化数据存储"""
        # 初始化Redis连接
        # 初始化数据库连接
        pass
    
    async def _validate_configuration(self) -> None:
        """验证配置"""
        # 验证AI模型配置
        # 验证Crypto配置
        # 验证数据库配置
        pass
    
    async def _stop_running_tasks(self) -> None:
        """停止所有运行中的任务"""
        # 取消所有后台任务
        for execution_id, task in self._background_tasks.items():
            if not task.done():
                self.logger.info(f"Cancelling background task for execution {execution_id}")
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass

        self._background_tasks.clear()

    def _cleanup_background_task(self, execution_id: UUID, task: asyncio.Task) -> None:
        """清理完成的后台任务"""
        try:
            # 移除已完成的任务
            self._background_tasks.pop(execution_id, None)

            # 检查任务是否有异常
            if task.exception():
                self.logger.error(
                    f"Background task for execution {execution_id} failed: {task.exception()}"
                )
        except Exception as e:
            self.logger.error(f"Error cleaning up background task {execution_id}: {e}")
    
    async def _cleanup_connections(self) -> None:
        """清理连接"""
        # 清理数据库连接
        # 清理Redis连接
        pass
    
    async def _generate_test_cases_impl(
        self, request: TestCaseGenerationRequest
    ) -> List[TestCaseDefinition]:
        """
        实际的测试用例生成实现
        
        Args:
            request: 测试用例生成请求
            
        Returns:
            List[TestCaseDefinition]: 生成的测试用例列表
        """
        try:
            # 使用TestCaseGenerator生成测试用例
            from src.test_case.models import GenerationContext, GenerationOptions
            
            # 构建生成上下文
            context = GenerationContext(
                session_id=request.session_id,
                domain=request.context.get("domain"),
                system_type=request.context.get("system_type"),
                technology_stack=request.context.get("technology_stack", []),
                security_requirements=request.context.get("security_requirements", {}),
                crypto_enabled=request.crypto_enabled
            )
            
            # 构建生成选项
            options = GenerationOptions(
                target_coverage=request.target_coverage,
                max_test_cases=request.generation_options.get("max_test_cases", 20),
                include_boundary_tests=request.generation_options.get("include_boundary_tests", True),
                include_exception_tests=request.generation_options.get("include_exception_tests", True),
                include_security_tests=request.generation_options.get("include_security_tests", False),
                crypto_enabled=request.crypto_enabled,
                signature_enabled=request.crypto_enabled,
                risk_assessment_enabled=True,
                use_templates=True,
                generate_test_data=True,
            )
            
            # 调用生成器 - 修正参数传递
            generated_cases = await self.test_case_generator.generate_test_cases(
                requirement_text=request.requirement_text,
                context=context,
                options=options
            )
            
            # 转换为Orchestrator模型格式
            test_cases = []
            for case in generated_cases:
                orchestrator_case = TestCaseDefinition(
                    test_case_id=case.id,
                    title=case.title,
                    description=case.description,
                    scenario=case.scenario or case.description,
                    preconditions=case.preconditions,
                    test_steps=case.test_steps or case.steps,
                    expected_results=case.expected_results or [case.expected_result] if case.expected_result else [],
                    test_data=case.test_data,
                    priority=case.priority,
                    tags=case.tags,
                    risk_level=case.risk_level,
                    created_at=case.created_at
                )
                test_cases.append(orchestrator_case)
            
            return test_cases
            
        except Exception as e:
            self.logger.error(f"Test case generation implementation failed: {e}")
            # 返回模拟数据作为后备
            return [
                TestCaseDefinition(
                    title="示例测试用例",
                    description="这是一个示例测试用例",
                    scenario="基本功能测试场景",
                    preconditions=["系统正常运行"],
                    test_steps=["执行基本操作", "验证结果"],
                    expected_results=["操作成功", "结果正确"],
                    test_data={"input": "test_data", "expected": "success"},
                    priority="medium",
                    tags=["example", "basic"],
                    risk_level="low"
                )
            ]
    
    async def _generate_scripts_impl(
        self, request: ScriptGenerationRequest
    ) -> List[ScriptDefinition]:
        """
        脚本生成实现 (设计文档3.4要求)

        Args:
            request: 脚本生成请求

        Returns:
            List[ScriptDefinition]: 生成的脚本列表
        """
        try:
            # 检查模板引擎是否可用
            if self.template_engine is None:
                self.logger.warning("Template engine not available, using mock implementation")
                return await self._generate_scripts_mock(request)

            # 调用实际的脚本生成模块
            generated_scripts = await self.template_engine.generate_scripts(
                test_case_ids=request.test_case_ids,
                target_language=request.target_language,
                target_framework=request.target_framework,
                crypto_enabled=request.crypto_config is not None,
                environment=request.environment_config.get("environment", "test")
            )

            # 添加加密相关逻辑 (设计文档3.4安全集成)
            if request.crypto_config and self.crypto_client is not None:
                for script in generated_scripts:
                    try:
                        # 添加加密签名逻辑到脚本中
                        script.content = await self.crypto_client.add_crypto_flows(
                            script.content, request.crypto_config
                        )
                    except Exception as crypto_error:
                        self.logger.warning(f"Failed to add crypto flows to script {script.script_id}: {crypto_error}")

            return generated_scripts

        except Exception as e:
            self.logger.error(f"Script generation failed: {e}")
            # 返回模拟脚本作为后备
            return await self._generate_scripts_mock(request)

    async def _generate_scripts_mock(self, request: ScriptGenerationRequest) -> List[ScriptDefinition]:
        """生成模拟脚本"""
        scripts = []
        for i, test_case_id in enumerate(request.test_case_ids):
            script = ScriptDefinition(
                script_id=uuid4(),
                test_case_id=test_case_id,
                language=request.target_language,
                framework=request.target_framework,
                content=f"# Mock script for test case {test_case_id}\n# Language: {request.target_language}\n# Framework: {request.target_framework}\n\ndef test_case_{i}():\n    pass",
                metadata={
                    "generated_at": datetime.now().isoformat(),
                    "mock": True
                }
            )
            scripts.append(script)
        return scripts
    
    async def _execute_scripts_background(
        self, execution_id: UUID, request: ExecutionRequest
    ) -> None:
        """
        后台执行脚本 (设计文档3.5要求调用调度器)

        Args:
            execution_id: 执行ID
            request: 执行请求
        """
        execution = self._executions.get(execution_id)
        if not execution:
            self.logger.error(f"Execution {execution_id} not found")
            return

        try:
            # 使用锁保护执行状态更新
            async with self._execution_lock:
                execution.status = ExecutionState.RUNNING

            # 检查是否有脚本执行器
            if self.script_executor is not None:
                self.logger.info("Using real script executor")
                await self._execute_scripts_real(execution_id, request)
                return
            
            # 检查调度器是否可用
            if self.scheduler is None:
                self.logger.warning("Scheduler not available, using mock execution")
                await self._execute_scripts_mock(execution_id, request)
                return

            # 真实调用执行调度模块
            await self.scheduler.schedule_execution(
                execution_id=execution_id,
                script_ids=request.script_ids,
                environment=request.environment
            )

            # 监控执行状态
            max_wait_time = 300  # 5分钟超时
            wait_time = 0

            while not await self.scheduler.execution_completed(execution_id) and wait_time < max_wait_time:
                await asyncio.sleep(1)
                wait_time += 1

                try:
                    status = await self.scheduler.get_execution_status(execution_id)
                    async with self._execution_lock:
                        execution.completed_scripts = status.completed_scripts
                        execution.failed_scripts = status.failed_scripts
                        execution.progress = status.progress
                except Exception as status_error:
                    self.logger.warning(f"Failed to get execution status: {status_error}")

            if wait_time >= max_wait_time:
                raise TimeoutError(f"Execution {execution_id} timed out after {max_wait_time} seconds")

            # 分析执行结果 (设计文档3.6要求)
            if self.execution_analyzer is not None:
                try:
                    report = await self.execution_analyzer.analyze_execution(execution_id)
                    execution.analysis_report = report
                except Exception as analysis_error:
                    self.logger.warning(f"Failed to analyze execution: {analysis_error}")

            async with self._execution_lock:
                execution.status = ExecutionState.COMPLETED
                execution.end_time = datetime.now()

            self.logger.info(
                f"Test execution completed: {execution_id} in {(execution.end_time - execution.start_time).total_seconds():.2f}s"
            )

        except Exception as e:
            async with self._execution_lock:
                execution.status = ExecutionState.FAILED
                execution.error_message = str(e)
                execution.end_time = datetime.now()

            self.logger.error(
                f"Test execution failed: {execution_id} - {e}"
            )

            # 发送失败通知 (设计文档3.6要求)
            if self.execution_analyzer is not None:
                try:
                    await self.execution_analyzer.report_failure(execution_id, str(e))
                except Exception as report_error:
                    self.logger.error(f"Failed to report execution failure: {report_error}")

    async def _execute_scripts_mock(self, execution_id: UUID, request: ExecutionRequest) -> None:
        """模拟脚本执行"""
        execution = self._executions.get(execution_id)
        if not execution:
            return

        try:
            # 模拟执行过程
            total_scripts = len(request.script_ids)
            for i, script_id in enumerate(request.script_ids):
                await asyncio.sleep(0.1)  # 模拟执行时间

                async with self._execution_lock:
                    execution.completed_scripts = i + 1
                    execution.progress = (i + 1) / total_scripts

            async with self._execution_lock:
                execution.status = ExecutionState.COMPLETED
                execution.end_time = datetime.now()

            self.logger.info(f"Mock execution completed: {execution_id}")

        except Exception as e:
            async with self._execution_lock:
                execution.status = ExecutionState.FAILED
                execution.error_message = str(e)
                execution.end_time = datetime.now()

            self.logger.error(f"Mock execution failed: {execution_id} - {e}")

    async def _execute_scripts_real(self, execution_id: UUID, request: ExecutionRequest) -> None:
        """使用真实的脚本执行器执行脚本"""
        execution = self._executions.get(execution_id)
        if not execution:
            self.logger.error(f"Execution {execution_id} not found")
            return

        try:
            self.logger.info(f"Starting real script execution: {execution_id}")
            
            # 获取脚本内容（这里假设我们有脚本内容存储）
            scripts_to_execute = []
            for script_id in request.script_ids:
                # 这里应该从存储中获取脚本内容
                # 暂时使用模拟数据
                scripts_to_execute.append({
                    "script_id": script_id,
                    "content": f"# Test script {script_id}\nprint('Executing test script {script_id}')",
                    "language": "python",
                    "environment_vars": request.environment_config.get("env_vars", {}),
                    "execution_options": request.environment_config.get("options", {})
                })
            
            # 使用脚本执行器批量执行
            from src.scheduler.executor import ScriptLanguage
            
            # 转换为执行器支持的格式
            executor_scripts = []
            for script in scripts_to_execute:
                executor_scripts.append({
                    "script_id": script["script_id"],
                    "content": script["content"],
                    "language": script["language"],
                    "environment_vars": script.get("environment_vars"),
                    "execution_options": script.get("execution_options")
                })
            
            # 执行脚本
            results = await self.script_executor.execute_scripts_batch(
                scripts=executor_scripts,
                parallel=request.execution_config.get("parallel", True)
            )
            
            # 处理执行结果
            completed_count = 0
            failed_count = 0
            
            async with self._execution_lock:
                for result in results:
                    if result.status.value == "completed":
                        completed_count += 1
                    elif result.status.value == "failed":
                        failed_count += 1
                
                execution.completed_scripts = completed_count
                execution.failed_scripts = failed_count
                execution.progress = len(results) / len(request.script_ids) if request.script_ids else 1.0
                
                if failed_count == 0:
                    execution.status = ExecutionState.COMPLETED
                else:
                    execution.status = ExecutionState.FAILED if completed_count == 0 else ExecutionState.COMPLETED
                
                execution.end_time = datetime.now()
            
            # 如果有分析器，分析执行结果
            if self.execution_analyzer is not None:
                try:
                    from src.analysis.analyzer import AnalysisType
                    
                    # 转换执行结果为日志格式
                    execution_logs = []
                    for result in results:
                        log_entry = {
                            "timestamp": result.start_time.isoformat() if result.start_time else datetime.now().isoformat(),
                            "script_id": str(result.script_id),
                            "status": result.status.value,
                            "exit_code": result.exit_code,
                            "stdout": result.stdout,
                            "stderr": result.stderr,
                            "duration": (result.end_time - result.start_time).total_seconds() if result.start_time and result.end_time else 0
                        }
                        execution_logs.append(log_entry)
                    
                    # 执行分析
                    analysis_results = await self.execution_analyzer.analyze_execution(
                        execution_id=execution_id,
                        execution_logs=execution_logs,
                        analysis_types=[
                            AnalysisType.EXECUTION_SUMMARY,
                            AnalysisType.PERFORMANCE_ANALYSIS,
                            AnalysisType.FAILURE_ANALYSIS if failed_count > 0 else AnalysisType.EXECUTION_SUMMARY
                        ]
                    )
                    
                    # 存储分析结果
                    execution.analysis_report = [result.to_dict() for result in analysis_results]
                    
                    self.logger.info(f"Execution analysis completed: {len(analysis_results)} results")
                    
                except Exception as e:
                    self.logger.error(f"Execution analysis failed: {e}")
            
            self.logger.info(f"Real script execution completed: {execution_id}, {completed_count} succeeded, {failed_count} failed")
            
        except Exception as e:
            async with self._execution_lock:
                execution.status = ExecutionState.FAILED
                execution.error_message = str(e)
                execution.end_time = datetime.now()
            
            self.logger.error(f"Real script execution failed: {execution_id} - {e}")
            
            # 如果有反馈处理器，提交错误反馈
            if self.feedback_processor is not None:
                try:
                    from src.feedback.processor import FeedbackType, FeedbackSeverity
                    
                    await self.feedback_processor.submit_feedback(
                        feedback_type=FeedbackType.EXECUTION_ISSUE,
                        title=f"Script execution failed for execution {execution_id}",
                        description=f"Script execution failed with error: {str(e)}",
                        source_id=execution_id,
                        severity=FeedbackSeverity.HIGH,
                        metadata={
                            "execution_id": str(execution_id),
                            "script_count": len(request.script_ids),
                            "error_type": type(e).__name__
                        }
                    )
                except Exception as feedback_error:
                    self.logger.error(f"Failed to submit feedback for execution failure: {feedback_error}")
    
    def _register_orchestrator_health_checks(self) -> None:
        """注册Orchestrator特定的健康检查"""
        if not self.system_monitor:
            return
        
        def check_orchestrator_health() -> Dict[str, Any]:
            """检查Orchestrator服务健康状态"""
            try:
                from src.common.monitor import HealthStatus
                
                status = HealthStatus.HEALTHY
                messages = []
                details = {}
                
                # 检查是否已初始化
                if not self._initialized:
                    status = HealthStatus.CRITICAL
                    messages.append("Orchestrator not initialized")
                
                # 检查活跃会话数量
                active_sessions = len(self._sessions)
                details["active_sessions"] = active_sessions
                if active_sessions > 100:  # 假设最大100个会话
                    status = HealthStatus.WARNING
                    messages.append(f"High number of active sessions: {active_sessions}")
                
                # 检查运行中的执行数量
                running_executions = len([e for e in self._executions.values() if e.status.value == "running"])
                details["running_executions"] = running_executions
                if running_executions > 50:  # 假设最大50个并发执行
                    status = HealthStatus.WARNING
                    messages.append(f"High number of running executions: {running_executions}")
                
                # 检查后台任务数量
                background_tasks = len(self._background_tasks)
                details["background_tasks"] = background_tasks
                if background_tasks > 20:
                    status = HealthStatus.WARNING
                    messages.append(f"High number of background tasks: {background_tasks}")
                
                # 检查模块状态
                module_status = {}
                for module_name in ['llm_client', 'crypto_client', 'test_case_generator', 
                                   'template_engine', 'script_executor', 'feedback_processor', 
                                   'execution_analyzer', 'auth_manager']:
                    module = getattr(self, module_name, None)
                    module_status[module_name] = "available" if module else "unavailable"
                
                details["modules"] = module_status
                
                return {
                    "status": status,
                    "message": "; ".join(messages) if messages else "Orchestrator is healthy",
                    "details": details
                }
                
            except Exception as e:
                return {
                    "status": HealthStatus.CRITICAL,
                    "message": f"Failed to check orchestrator health: {str(e)}",
                    "details": {"error": str(e)}
                }
        
        def check_ai_client_health() -> Dict[str, Any]:
            """检查AI客户端健康状态"""
            try:
                from src.common.monitor import HealthStatus
                
                if not self.llm_client:
                    return {
                        "status": HealthStatus.WARNING,
                        "message": "AI client not available",
                        "details": {"available": False}
                    }
                
                # 这里可以添加更具体的AI客户端健康检查
                # 比如测试连接、检查API限制等
                
                return {
                    "status": HealthStatus.HEALTHY,
                    "message": "AI client is available",
                    "details": {
                        "available": True,
                        "provider": getattr(self.llm_client, 'provider', 'unknown')
                    }
                }
                
            except Exception as e:
                return {
                    "status": HealthStatus.CRITICAL,
                    "message": f"Failed to check AI client health: {str(e)}",
                    "details": {"error": str(e)}
                }
        
        def check_crypto_client_health() -> Dict[str, Any]:
            """检查加密客户端健康状态"""
            try:
                from src.common.monitor import HealthStatus
                
                if not self.crypto_client:
                    return {
                        "status": HealthStatus.WARNING,
                        "message": "Crypto client not available",
                        "details": {"available": False}
                    }
                
                return {
                    "status": HealthStatus.HEALTHY,
                    "message": "Crypto client is available",
                    "details": {"available": True}
                }
                
            except Exception as e:
                return {
                    "status": HealthStatus.CRITICAL,
                    "message": f"Failed to check crypto client health: {str(e)}",
                    "details": {"error": str(e)}
                }
        
        def check_database_health() -> Dict[str, Any]:
            """检查数据库健康状态"""
            try:
                from src.common.monitor import HealthStatus
                
                if not self.database_manager:
                    return {
                        "status": HealthStatus.WARNING,
                        "message": "Database manager not available",
                        "details": {"available": False}
                    }
                
                if not self.database_manager.is_ready():
                    return {
                        "status": HealthStatus.CRITICAL,
                        "message": "Database manager not ready",
                        "details": {"ready": False}
                    }
                
                # 获取数据库统计信息
                stats = self.database_manager.get_statistics()
                
                return {
                    "status": HealthStatus.HEALTHY,
                    "message": "Database manager is healthy",
                    "details": stats
                }
                
            except Exception as e:
                return {
                    "status": HealthStatus.CRITICAL,
                    "message": f"Failed to check database health: {str(e)}",
                    "details": {"error": str(e)}
                }

        # 注册健康检查
        self.system_monitor.register_health_check("orchestrator", check_orchestrator_health)
        self.system_monitor.register_health_check("ai_client", check_ai_client_health)
        self.system_monitor.register_health_check("crypto_client", check_crypto_client_health)
        self.system_monitor.register_health_check("database", check_database_health)