"""
脚本执行器

负责实际执行测试脚本，支持多种执行环境和语言
"""

import asyncio
import subprocess
import tempfile
import shutil
import json
import os
import sys
import psutil
from typing import Dict, List, Optional, Any, Union
from uuid import UUID, uuid4
from datetime import datetime
from enum import Enum
from pathlib import Path

from src.common.logger import get_logger


class ExecutionEnvironment(str, Enum):
    """执行环境"""
    LOCAL = "local"
    DOCKER = "docker"
    KUBERNETES = "kubernetes"
    CLOUD = "cloud"


class ScriptLanguage(str, Enum):
    """脚本语言"""
    PYTHON = "python"
    JAVASCRIPT = "javascript"
    JAVA = "java"
    SHELL = "shell"
    POWERSHELL = "powershell"
    BATCH = "batch"


class ExecutionStatus(str, Enum):
    """执行状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    TIMEOUT = "timeout"


class ExecutionResult:
    """执行结果"""
    
    def __init__(
        self,
        script_id: UUID,
        status: ExecutionStatus,
        exit_code: int = 0,
        stdout: str = "",
        stderr: str = "",
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        resource_usage: Optional[Dict[str, Any]] = None,
        error_message: Optional[str] = None
    ):
        self.script_id = script_id
        self.status = status
        self.exit_code = exit_code
        self.stdout = stdout
        self.stderr = stderr
        self.start_time = start_time
        self.end_time = end_time
        self.resource_usage = resource_usage or {}
        self.error_message = error_message


class ScriptExecutor:
    """
    脚本执行器
    
    负责实际执行测试脚本，支持多种执行环境和语言
    """
    
    def __init__(self, working_dir: Optional[str] = None):
        self.logger = get_logger(__name__)
        self._initialized = False
        self._working_dir = working_dir or tempfile.gettempdir()
        
        # 执行状态管理
        self._running_executions: Dict[UUID, asyncio.Task] = {}
        self._execution_results: Dict[UUID, ExecutionResult] = {}
        
        # 资源监控
        self._enable_monitoring = True
        self._max_memory_mb = 1024  # 最大内存使用
        self._max_cpu_percent = 80  # 最大CPU使用率
        self._execution_timeout = 300  # 执行超时（秒）
        
        # 执行统计
        self._total_executions = 0
        self._successful_executions = 0
        self._failed_executions = 0
        self._cancelled_executions = 0
        
        # 支持的语言配置
        self._language_configs = {
            ScriptLanguage.PYTHON: {
                "command": "python",
                "extension": ".py",
                "timeout": 300,
                "args": []
            },
            ScriptLanguage.JAVASCRIPT: {
                "command": "node",
                "extension": ".js",
                "timeout": 180,
                "args": []
            },
            ScriptLanguage.JAVA: {
                "command": "java",
                "extension": ".java",
                "timeout": 600,
                "args": []
            },
            ScriptLanguage.SHELL: {
                "command": "bash" if os.name != "nt" else "cmd",
                "extension": ".sh" if os.name != "nt" else ".bat",
                "timeout": 300,
                "args": []
            },
            ScriptLanguage.POWERSHELL: {
                "command": "powershell",
                "extension": ".ps1",
                "timeout": 300,
                "args": ["-ExecutionPolicy", "Bypass"]
            }
        }
    
    async def initialize(self) -> None:
        """初始化执行器"""
        try:
            self.logger.info("Initializing ScriptExecutor")
            
            # 创建工作目录
            os.makedirs(self._working_dir, exist_ok=True)
            
            # 检查环境依赖
            await self._check_environment()
            
            self._initialized = True
            self.logger.info(f"ScriptExecutor initialized, working dir: {self._working_dir}")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize ScriptExecutor: {e}")
            raise
    
    async def cleanup(self) -> None:
        """清理资源"""
        try:
            self.logger.info("Cleaning up ScriptExecutor")
            
            # 取消所有运行中的执行
            if self._running_executions:
                self.logger.info(f"Cancelling {len(self._running_executions)} running executions")
                for execution_id, task in self._running_executions.items():
                    if not task.done():
                        task.cancel()
                
                # 等待所有任务结束
                await asyncio.gather(*self._running_executions.values(), return_exceptions=True)
            
            # 清理临时文件
            await self._cleanup_temp_files()
            
            # 清理状态
            self._running_executions.clear()
            self._execution_results.clear()
            
            self._initialized = False
            self.logger.info("ScriptExecutor cleaned up successfully")
            
        except Exception as e:
            self.logger.error(f"Error during ScriptExecutor cleanup: {e}")
    
    async def execute_script(
        self,
        script_id: UUID,
        script_content: str,
        language: ScriptLanguage,
        environment: ExecutionEnvironment = ExecutionEnvironment.LOCAL,
        timeout: Optional[int] = None,
        env_vars: Optional[Dict[str, str]] = None,
        working_directory: Optional[str] = None
    ) -> UUID:
        """
        执行单个脚本
        
        Args:
            script_id: 脚本ID
            script_content: 脚本内容
            language: 脚本语言
            environment: 执行环境
            timeout: 超时时间（秒）
            env_vars: 环境变量
            working_directory: 工作目录
            
        Returns:
            UUID: 执行ID
        """
        if not self._initialized:
            raise RuntimeError("ScriptExecutor not initialized")
        
        try:
            execution_id = uuid4()
            
            # 创建执行任务
            task = asyncio.create_task(
                self._execute_script_impl(
                    execution_id,
                    script_id,
                    script_content,
                    language,
                    environment,
                    timeout,
                    env_vars,
                    working_directory
                )
            )
            
            # 添加到运行中的任务
            self._running_executions[execution_id] = task
            
            self.logger.info(f"Started script execution: {execution_id} for script: {script_id}")
            return execution_id
            
        except Exception as e:
            self.logger.error(f"Failed to start script execution: {e}")
            raise
    
    async def execute_scripts_batch(
        self,
        scripts: List[Dict[str, Any]],
        environment: ExecutionEnvironment = ExecutionEnvironment.LOCAL,
        parallel: bool = True,
        max_concurrent: int = 5
    ) -> List[ExecutionResult]:
        """
        批量执行脚本
        
        Args:
            scripts: 脚本列表，每个包含 id, content, language 等信息
            environment: 执行环境
            parallel: 是否并行执行
            max_concurrent: 最大并发数
            
        Returns:
            List[ExecutionResult]: 执行结果列表
        """
        if not self._initialized:
            raise RuntimeError("ScriptExecutor not initialized")
        
        try:
            self.logger.info(f"Starting batch execution of {len(scripts)} scripts")
            results = []
            
            if parallel:
                # 并行执行
                semaphore = asyncio.Semaphore(max_concurrent)
                
                async def execute_with_semaphore(script_info):
                    async with semaphore:
                        execution_id = await self.execute_script(
                            script_id=script_info["id"],
                            script_content=script_info["content"],
                            language=ScriptLanguage(script_info["language"]),
                            environment=environment,
                            timeout=script_info.get("timeout"),
                            env_vars=script_info.get("env_vars"),
                            working_directory=script_info.get("working_directory")
                        )
                        return await self.wait_for_completion(execution_id)
                
                # 启动所有任务
                tasks = [execute_with_semaphore(script) for script in scripts]
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # 处理异常结果
                processed_results = []
                for i, result in enumerate(results):
                    if isinstance(result, Exception):
                        error_result = ExecutionResult(
                            script_id=scripts[i]["id"],
                            status=ExecutionStatus.FAILED,
                            exit_code=-1,
                            error_message=str(result)
                        )
                        processed_results.append(error_result)
                    else:
                        processed_results.append(result)
                
                results = processed_results
            else:
                # 串行执行
                for script_info in scripts:
                    try:
                        execution_id = await self.execute_script(
                            script_id=script_info["id"],
                            script_content=script_info["content"],
                            language=ScriptLanguage(script_info["language"]),
                            environment=environment,
                            timeout=script_info.get("timeout"),
                            env_vars=script_info.get("env_vars"),
                            working_directory=script_info.get("working_directory")
                        )
                        result = await self.wait_for_completion(execution_id)
                        results.append(result)
                    except Exception as e:
                        error_result = ExecutionResult(
                            script_id=script_info["id"],
                            status=ExecutionStatus.FAILED,
                            exit_code=-1,
                            error_message=str(e)
                        )
                        results.append(error_result)
            
            self.logger.info(f"Batch execution completed: {len(results)} results")
            return results
            
        except Exception as e:
            self.logger.error(f"Failed to execute scripts batch: {e}")
            raise
    
    async def get_execution_status(self, execution_id: UUID) -> Optional[ExecutionResult]:
        """获取执行状态"""
        try:
            # 检查是否在运行中
            if execution_id in self._running_executions:
                task = self._running_executions[execution_id]
                if task.done():
                    try:
                        result = await task
                        return result
                    except Exception as e:
                        return ExecutionResult(
                            script_id=uuid4(),
                            status=ExecutionStatus.FAILED,
                            error_message=str(e)
                        )
                else:
                    # 任务仍在运行
                    return ExecutionResult(
                        script_id=uuid4(),
                        status=ExecutionStatus.RUNNING
                    )
            
            # 检查已完成的结果
            return self._execution_results.get(execution_id)
            
        except Exception as e:
            self.logger.error(f"Failed to get execution status for {execution_id}: {e}")
            return None
    
    async def cancel_execution(self, execution_id: UUID) -> bool:
        """取消执行"""
        try:
            if execution_id in self._running_executions:
                task = self._running_executions[execution_id]
                if not task.done():
                    task.cancel()
                    
                    # 等待任务结束
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass
                    
                    # 更新统计
                    self._cancelled_executions += 1
                    
                    self.logger.info(f"Execution {execution_id} cancelled")
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to cancel execution {execution_id}: {e}")
            return False
    
    async def wait_for_completion(self, execution_id: UUID, timeout: Optional[int] = None) -> ExecutionResult:
        """等待执行完成"""
        try:
            if execution_id not in self._running_executions:
                # 检查是否已有结果
                if execution_id in self._execution_results:
                    return self._execution_results[execution_id]
                raise ValueError(f"Execution {execution_id} not found")
            
            task = self._running_executions[execution_id]
            
            if timeout:
                result = await asyncio.wait_for(task, timeout=timeout)
            else:
                result = await task
            
            return result
            
        except asyncio.TimeoutError:
            # 超时，取消任务
            await self.cancel_execution(execution_id)
            return ExecutionResult(
                script_id=uuid4(),
                status=ExecutionStatus.TIMEOUT,
                error_message=f"Execution timed out after {timeout} seconds"
            )
        except Exception as e:
            self.logger.error(f"Error waiting for execution {execution_id}: {e}")
            return ExecutionResult(
                script_id=uuid4(),
                status=ExecutionStatus.FAILED,
                error_message=str(e)
            )
    
    async def _execute_script_impl(
        self,
        execution_id: UUID,
        script_id: UUID,
        script_content: str,
        language: ScriptLanguage,
        environment: ExecutionEnvironment,
        timeout: Optional[int],
        env_vars: Optional[Dict[str, str]],
        working_directory: Optional[str]
    ) -> ExecutionResult:
        """执行脚本的具体实现"""
        result = ExecutionResult(script_id=script_id, status=ExecutionStatus.PENDING)
        temp_script_path = None
        
        try:
            self.logger.info(f"Executing script {script_id} in {environment} environment")
            
            # 更新统计
            self._total_executions += 1
            
            # 设置超时
            if timeout is None:
                timeout = self._language_configs.get(language, {}).get("timeout", self._execution_timeout)
            
            # 根据环境选择执行方式
            if environment == ExecutionEnvironment.LOCAL:
                result = await self._execute_local(
                    script_id, script_content, language, timeout, env_vars, working_directory
                )
            elif environment == ExecutionEnvironment.DOCKER:
                result = await self._execute_docker(
                    script_id, script_content, language, timeout, env_vars
                )
            elif environment == ExecutionEnvironment.KUBERNETES:
                result = await self._execute_kubernetes(
                    script_id, script_content, language, timeout, env_vars
                )
            else:
                raise ValueError(f"Unsupported execution environment: {environment}")
            
            # 更新统计
            if result.status == ExecutionStatus.COMPLETED:
                self._successful_executions += 1
            else:
                self._failed_executions += 1
            
            # 存储结果
            self._execution_results[execution_id] = result
            
            # 从运行中的任务移除
            if execution_id in self._running_executions:
                del self._running_executions[execution_id]
            
            self.logger.info(f"Script execution completed: {script_id}, status: {result.status}")
            return result
            
        except asyncio.CancelledError:
            result.status = ExecutionStatus.CANCELLED
            result.error_message = "Execution was cancelled"
            self._cancelled_executions += 1
            self.logger.info(f"Script execution cancelled: {script_id}")
            raise
        except Exception as e:
            result.status = ExecutionStatus.FAILED
            result.error_message = str(e)
            result.end_time = datetime.now()
            self._failed_executions += 1
            
            self.logger.error(f"Script execution failed: {script_id}, error: {e}")
            return result
        finally:
            # 清理临时文件
            if temp_script_path and os.path.exists(temp_script_path):
                try:
                    os.remove(temp_script_path)
                except Exception as cleanup_error:
                    self.logger.warning(f"Failed to cleanup temp file {temp_script_path}: {cleanup_error}")
    
    async def _execute_local(
        self,
        script_id: UUID,
        script_content: str,
        language: ScriptLanguage,
        timeout: int,
        env_vars: Optional[Dict[str, str]],
        working_directory: Optional[str]
    ) -> ExecutionResult:
        """本地执行脚本"""
        result = ExecutionResult(script_id=script_id, status=ExecutionStatus.RUNNING)
        result.start_time = datetime.now()
        
        try:
            # 获取语言配置
            lang_config = self._language_configs.get(language)
            if not lang_config:
                raise ValueError(f"Unsupported language: {language}")
            
            # 创建临时脚本文件
            script_path = await self._create_temp_script(script_content, language)
            
            # 准备执行命令
            command = [lang_config["command"]]
            command.extend(lang_config.get("args", []))
            command.append(script_path)
            
            # 准备环境变量
            env = os.environ.copy()
            if env_vars:
                env.update(env_vars)
            
            # 设置工作目录
            cwd = working_directory or self._working_dir
            
            # 启动进程
            process = await asyncio.create_subprocess_exec(
                *command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                env=env,
                cwd=cwd
            )
            
            # 启动资源监控
            monitor_task = None
            if self._enable_monitoring:
                monitor_task = asyncio.create_task(
                    self._monitor_process_resources(process.pid, result)
                )
            
            try:
                # 等待进程完成
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(),
                    timeout=timeout
                )
                
                result.exit_code = process.returncode
                result.stdout = stdout.decode('utf-8', errors='ignore')
                result.stderr = stderr.decode('utf-8', errors='ignore')
                
                if process.returncode == 0:
                    result.status = ExecutionStatus.COMPLETED
                else:
                    result.status = ExecutionStatus.FAILED
                    result.error_message = f"Process exited with code {process.returncode}"
                
            except asyncio.TimeoutError:
                # 超时，杀死进程
                try:
                    process.kill()
                    await process.wait()
                except Exception:
                    pass
                
                result.status = ExecutionStatus.TIMEOUT
                result.error_message = f"Execution timed out after {timeout} seconds"
            
            finally:
                # 停止监控
                if monitor_task:
                    monitor_task.cancel()
                    try:
                        await monitor_task
                    except asyncio.CancelledError:
                        pass
                
                # 清理脚本文件
                try:
                    os.remove(script_path)
                except Exception:
                    pass
            
            result.end_time = datetime.now()
            return result
            
        except Exception as e:
            result.status = ExecutionStatus.FAILED
            result.error_message = str(e)
            result.end_time = datetime.now()
            return result
    
    async def _execute_docker(
        self,
        script_id: UUID,
        script_content: str,
        language: ScriptLanguage,
        timeout: int,
        env_vars: Optional[Dict[str, str]]
    ) -> ExecutionResult:
        """Docker环境执行脚本"""
        result = ExecutionResult(script_id=script_id, status=ExecutionStatus.FAILED)
        result.start_time = datetime.now()
        result.error_message = "Docker execution not implemented"
        result.end_time = datetime.now()
        return result
    
    async def _execute_kubernetes(
        self,
        script_id: UUID,
        script_content: str,
        language: ScriptLanguage,
        timeout: int,
        env_vars: Optional[Dict[str, str]]
    ) -> ExecutionResult:
        """Kubernetes环境执行脚本"""
        result = ExecutionResult(script_id=script_id, status=ExecutionStatus.FAILED)
        result.start_time = datetime.now()
        result.error_message = "Kubernetes execution not implemented"
        result.end_time = datetime.now()
        return result
    
    async def _create_temp_script(self, content: str, language: ScriptLanguage) -> str:
        """创建临时脚本文件"""
        try:
            lang_config = self._language_configs.get(language)
            if not lang_config:
                raise ValueError(f"Unsupported language: {language}")
            
            extension = lang_config["extension"]
            
            # 创建临时文件
            with tempfile.NamedTemporaryFile(
                mode='w',
                suffix=extension,
                dir=self._working_dir,
                delete=False,
                encoding='utf-8'
            ) as f:
                f.write(content)
                temp_path = f.name
            
            # 设置执行权限（Linux/Mac）
            if os.name != 'nt':
                os.chmod(temp_path, 0o755)
            
            return temp_path
            
        except Exception as e:
            self.logger.error(f"Failed to create temp script: {e}")
            raise
    
    async def _monitor_process_resources(self, pid: int, result: ExecutionResult) -> None:
        """监控进程资源使用"""
        try:
            process = psutil.Process(pid)
            max_memory = 0
            max_cpu = 0
            
            while process.is_running():
                try:
                    # 获取内存使用
                    memory_info = process.memory_info()
                    memory_mb = memory_info.rss / 1024 / 1024
                    max_memory = max(max_memory, memory_mb)
                    
                    # 获取CPU使用率
                    cpu_percent = process.cpu_percent()
                    max_cpu = max(max_cpu, cpu_percent)
                    
                    # 检查资源限制
                    if memory_mb > self._max_memory_mb:
                        self.logger.warning(f"Process {pid} exceeded memory limit: {memory_mb:.2f}MB")
                    
                    if cpu_percent > self._max_cpu_percent:
                        self.logger.warning(f"Process {pid} exceeded CPU limit: {cpu_percent:.2f}%")
                    
                    await asyncio.sleep(1)
                    
                except psutil.NoSuchProcess:
                    break
                except Exception as e:
                    self.logger.warning(f"Error monitoring process {pid}: {e}")
                    break
            
            # 更新资源使用信息
            result.resource_usage = {
                "max_memory_mb": max_memory,
                "max_cpu_percent": max_cpu,
                "monitoring_enabled": True
            }
            
        except Exception as e:
            self.logger.warning(f"Failed to monitor process resources: {e}")
            result.resource_usage = {
                "monitoring_enabled": False,
                "error": str(e)
            }
    
    async def _check_environment(self) -> None:
        """检查执行环境"""
        try:
            # 检查支持的语言环境
            for language, config in self._language_configs.items():
                command = config["command"]
                try:
                    # 尝试执行 --version 或 -v 命令
                    process = await asyncio.create_subprocess_exec(
                        command, "--version",
                        stdout=asyncio.subprocess.PIPE,
                        stderr=asyncio.subprocess.PIPE
                    )
                    await process.communicate()
                    
                    if process.returncode == 0:
                        self.logger.info(f"Language {language.value} is available ({command})")
                    else:
                        self.logger.warning(f"Language {language.value} may not be available ({command})")
                        
                except Exception as e:
                    self.logger.warning(f"Failed to check {language.value} environment: {e}")
            
        except Exception as e:
            self.logger.error(f"Failed to check environment: {e}")
    
    async def _cleanup_temp_files(self) -> None:
        """清理临时文件"""
        try:
            # 清理工作目录中的临时文件
            temp_dir = Path(self._working_dir)
            if temp_dir.exists():
                for file_path in temp_dir.glob("tmp*"):
                    try:
                        if file_path.is_file():
                            file_path.unlink()
                        elif file_path.is_dir():
                            shutil.rmtree(file_path)
                    except Exception as e:
                        self.logger.warning(f"Failed to cleanup temp file {file_path}: {e}")
            
        except Exception as e:
            self.logger.error(f"Failed to cleanup temp files: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            "total_executions": self._total_executions,
            "successful_executions": self._successful_executions,
            "failed_executions": self._failed_executions,
            "cancelled_executions": self._cancelled_executions,
            "running_executions": len(self._running_executions),
            "cached_results": len(self._execution_results),
            "working_directory": self._working_dir,
            "supported_languages": list(self._language_configs.keys()),
            "monitoring_enabled": self._enable_monitoring,
            "initialized": self._initialized
        } 