#!/usr/bin/env python3
"""
前端文件服务器

为TestGenius前端界面提供HTTP服务
"""

import http.server
import socketserver
import os
import sys
from pathlib import Path

def serve_frontend(port=3000):
    """启动前端服务器"""
    
    # 切换到frontend目录
    frontend_dir = Path(__file__).parent / "frontend"
    if not frontend_dir.exists():
        print("❌ frontend目录不存在")
        return False
    
    os.chdir(frontend_dir)
    
    # 创建HTTP服务器
    Handler = http.server.SimpleHTTPRequestHandler
    
    try:
        with socketserver.TCPServer(("", port), Handler) as httpd:
            print(f"🌐 前端服务器启动成功")
            print(f"📍 访问地址: http://localhost:{port}")
            print(f"📁 服务目录: {frontend_dir.absolute()}")
            print("按 Ctrl+C 停止服务器")
            
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n⏹️ 服务器已停止")
        return True
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
        return False

if __name__ == "__main__":
    port = 3000
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print("❌ 端口号必须是数字")
            sys.exit(1)
    
    serve_frontend(port) 